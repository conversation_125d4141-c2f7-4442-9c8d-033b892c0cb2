import { createApp } from 'vue'
import './style.css'
import App from './App.vue'
import router from './router'
import axios from 'axios';

const app = createApp(App);

export const baseURL = import.meta.env.VITE_BASE_URL;

export const redirectUri = import.meta.env.VITE_AD_REDIRECT_URI;

axios.defaults.baseURL = baseURL;

app.provide("baseImageUrl", import.meta.env.VITE_BASE_PATH + "/assets");

app.config.globalProperties.baseImgUrl = import.meta.env.VITE_BASE_PATH + "/assets";

app.use(router);

app.mount("#app");
