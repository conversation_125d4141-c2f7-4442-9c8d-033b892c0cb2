<script setup lang="ts">
import { defineProps, onMounted, onBeforeUnmount, ref, watch } from 'vue';
import Chart from 'chart.js/auto';
import annotationPlugin from 'chartjs-plugin-annotation';

Chart.register(annotationPlugin);

interface ChartDataPoint {
  x: number;
  y: number;
}

const props = defineProps({
  predictedData: { type: Array as () => ChartDataPoint[], required: true },
  originalData: { type: Array as () => ChartDataPoint[], required: true },
  r2: { type: Number, required: true },
  ridgeR2: { type: Number, required: true },
  diff: { type: Number, required: true },
  title: { type: String, required: true }
});

const chartCanvas = ref<HTMLCanvasElement | null>(null);
let chartInstance: Chart | null = null;

function getMaxPoint() {
  const maxY = Math.max(...props.predictedData.map(p => p.y));
  return props.predictedData.find(p => p.y === maxY) || { x: 0, y: 0 };
}

function createChart() {
  if (!chartCanvas.value) return;

  const maxPoint = getMaxPoint();

  chartInstance = new Chart(chartCanvas.value, {
    type: 'line',
    data: {
      datasets: [
        {
          label: 'Predicted Productivity',
          data: props.predictedData,
          borderColor: '#007bff',
          backgroundColor: '#007bff',
          borderWidth: 3,
          fill: false,
          tension: 0.4,
          parsing: { xAxisKey: 'x', yAxisKey: 'y' }
        },
        {
          label: 'Original Data',
          data: props.originalData,
          borderColor: '#dc2626',
          backgroundColor: '#dc2626',
          type: 'scatter',
          pointRadius: 5,
          showLine: false,
          parsing: { xAxisKey: 'x', yAxisKey: 'y' }
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top',
          labels: { font: { size: 12 } }
        },
        title: {
          display: true,
          text: props.title,
          font: { size: 16 }
        },
        tooltip: { enabled: false },
        annotation: {
          annotations: {
            lineX: {
              type: 'line',
              xMin: maxPoint.x,
              xMax: maxPoint.x,
              borderColor: '#059669',
              borderWidth: 1,
              borderDash: [6, 6],
              label: {
                content: `Saturation: ${maxPoint.x}`,
                enabled: true,
                position: 'start',
                backgroundColor: '#fff',
                color: '#059669',
                font: { size: 12 },
                yAdjust: -20
              }
            }
          }
        }
      },
      scales: {
        x: {
          type: 'linear',
          title: { display: true, text: 'Linear Meter (LM)' },
          ticks: { callback: (val: any) => Number(val).toFixed(1) }
        },
        y: {
          title: { display: true, text: 'Productivity' },
          ticks: { callback: (val: any) => Number(val).toFixed(1) }
        }
      }
    }
  });
}

onMounted(() => {
  createChart();
});

watch(props, () => {
  if (chartInstance) {
    chartInstance.destroy();
  }
  createChart();
}, { deep: true });

onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.destroy();
  }
});
</script>

<template>
  <div style="height: 350px;">
    <canvas ref="chartCanvas"></canvas>
  </div>
</template>
