<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import TableRow from './TableRow.vue';
import GroupTotalRow from './GroupTotalRow.vue';
import { storeData } from '../data/storeData';

const sortColumn = ref('');
const sortDirection = ref('asc');
const expandedGroups = ref<string[]>([]);

// Edit mode state for columns
const editColumn = ref<'spaceChange' | 'gmvChange' | null>(null);
// Temporary values for editing
const editValues = ref<Record<string, number>>({});

const sortData = (column: string) => {
  if (sortColumn.value === column) {
    sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc';
  } else {
    sortColumn.value = column;
    sortDirection.value = 'asc';
  }
};

const toggleGroup = (group: string) => {
  const index = expandedGroups.value.indexOf(group);
  if (index > -1) {
    expandedGroups.value.splice(index, 1);
  } else {
    expandedGroups.value.push(group);
  }
};

const groupedData = computed(() => {
  const groups: Record<string, any[]> = {};
  storeData.forEach(item => {
    if (!groups[item.group]) {
      groups[item.group] = [];
    }
    groups[item.group].push(item);
  });
  return groups;
});

const groupTotals = computed(() => {
  const totals: Record<string, any> = {};
  Object.keys(groupedData.value).forEach(group => {
    const items = groupedData.value[group];
    totals[group] = {
      storeCode: '21404',
      storeName: 'Dalma Mall',
      group: `${group} Total`,
      productivity: items.reduce((sum, item) => sum + item.productivity, 0) / items.length,
      currentLm: items.reduce((sum, item) => sum + item.currentLm, 0),
      optimizedLm: items.reduce((sum, item) => sum + item.optimizedLm, 0),
      optimizedGmv: items.reduce((sum, item) => sum + item.optimizedGmv, 0),
      gmvChange: (items.reduce((sum, item) => sum + item.optimizedGmv, 0) /
        items.reduce((sum, item) => sum + item.gmv, 0) - 1) * 100
    };
  });
  return totals;
});

const isGroupExpanded = (group: string) => {
  return expandedGroups.value.includes(group);
};

function getRowKey(item: any) {
  return `${item.storeCode}-${item.department}`;
}

// Start editing a column
function startEditColumn(col: 'spaceChange' | 'gmvChange') {
  editColumn.value = col;
  // Fill editValues with current values
  storeData.forEach(item => {
    editValues.value[getRowKey(item)] = Number(item[col]);
  });
}

// Save all edits for a column
function saveEditColumn(col: 'spaceChange' | 'gmvChange') {
  storeData.forEach(item => {
    const key = getRowKey(item);
    if (editValues.value[key] !== undefined) {
      item[col] = Number(editValues.value[key]);
    }
  });
  editColumn.value = null;
  editValues.value = {};
}

onMounted(() => {
  expandedGroups.value = [];
});
</script>

<template>
  <div class="overflow-x-auto">
    <table class="divide-y border border-table-border rounded-lg">
      <thead class="bg-table-header">
        <tr>
          <th v-for="(header, index) in [
            'Store Code', 'Store Name', 'Group', 'Department', 'Class', 'Subclass',
            'Productivity', 'GMV/LM Rank', 'Space Rank', 'Recommendation', 'Min',
            'Max', 'Current LM', 'Optimized LM', 'Space Change', 'Optimized GMV',
            'GMV Change'
          ]"
          :key="index"
          class="px-2 py-3 text-sm font-bold uppercase tracking-wider cursor-pointer border border-table-border text-center bg-table-header"
          :class="{ 'text-right': index >= 6 && index !== 9 && index !== 14 && index !== 16 }"
          @click="index !== 14 && index !== 16 ? sortData(header.toLowerCase()) : undefined"
          >
            <span>{{ header }}</span>
            <!-- Edit/Save icon for Space Change -->
            <template v-if="header === 'Space Change'">
              <button
                v-if="editColumn !== 'spaceChange'"
                @click.stop="startEditColumn('spaceChange')"
                class="ml-2"
                title="Edit Space Change"
              >
                <!-- Pencil SVG icon -->
                <svg xmlns="http://www.w3.org/2000/svg" class="inline w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536M9 13l6.586-6.586a2 2 0 112.828 2.828L11.828 15.828a2 2 0 01-2.828 0L9 13zm-6 6h6v-2a2 2 0 012-2h2a2 2 0 012 2v2h6"/></svg>
              </button>
              <button
                v-else
                @click.stop="saveEditColumn('spaceChange')"
                class="ml-2"
                title="Save Space Change"
              >
                <!-- Check SVG icon -->
                <svg xmlns="http://www.w3.org/2000/svg" class="inline w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/></svg>
              </button>
            </template>
            <!-- Edit/Save icon for GMV Change -->
            <template v-if="header === 'GMV Change'">
              <button
                v-if="editColumn !== 'gmvChange'"
                @click.stop="startEditColumn('gmvChange')"
                class="ml-2"
                title="Edit GMV Change"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="inline w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536M9 13l6.586-6.586a2 2 0 112.828 2.828L11.828 15.828a2 2 0 01-2.828 0L9 13zm-6 6h6v-2a2 2 0 012-2h2a2 2 0 012 2v2h6"/></svg>
              </button>
              <button
                v-else
                @click.stop="saveEditColumn('gmvChange')"
                class="ml-2"
                title="Save GMV Change"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="inline w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/></svg>
              </button>
            </template>
          </th>
        </tr>
      </thead>
      <tbody>
        <template v-for="(items, group) in groupedData" :key="group">
          <GroupTotalRow
            :group="group"
            :totals="groupTotals[group]"
            :is-expanded="isGroupExpanded(group)"
            @toggle="toggleGroup(group)"
            class="even:bg-table-row-even odd:bg-table-row-odd"
          />
          <template v-if="isGroupExpanded(group)">
            <tr
              v-for="(item, idx) in items.slice().reverse()"
              :key="`${item.storeCode}-${item.department}`"
            >
              <td class="px-2 py-2 text-sm border border-table-border text-center">{{ item.storeCode }}</td>
              <td class="px-2 py-2 text-sm border border-table-border text-center">{{ item.storeName }}</td>
              <td class="px-2 py-2 text-sm border border-table-border text-center">{{ item.group }}</td>
              <td class="px-2 py-2 text-sm border border-table-border text-center">{{ item.department }}</td>
              <td class="px-2 py-2 text-sm border border-table-border text-center">{{ item.class }}</td>
              <td class="px-2 py-2 text-sm border border-table-border text-center">{{ item.subclass }}</td>
              <td class="px-2 py-2 text-sm border border-table-border text-right">{{ item.productivity }}</td>
              <td class="px-2 py-2 text-sm border border-table-border text-right">{{ item.gmvLmRank }}</td>
              <td class="px-2 py-2 text-sm border border-table-border text-right">{{ item.spaceRank }}</td>
              <td class="px-2 py-2 text-sm border border-table-border text-center">{{ item.recommendation }}</td>
              <td class="px-2 py-2 text-sm border border-table-border text-right">{{ item.min }}</td>
              <td class="px-2 py-2 text-sm border border-table-border text-right">{{ item.max }}</td>
              <td class="px-2 py-2 text-sm border border-table-border text-right">{{ item.currentLm }}</td>
              <td class="px-2 py-2 text-sm border border-table-border text-right">{{ item.optimizedLm }}</td>
              <!-- Space Change Editable Column -->
              <td class="px-2 py-2 text-sm border border-table-border text-right">
                <template v-if="editColumn === 'spaceChange'">
                  <input
                    v-model.number="editValues[getRowKey(item)]"
                    class="border border-table-border rounded px-2 py-1 w-20"
                    type="number"
                  />
                </template>
                <template v-else>
                  {{ item.spaceChange }}
                </template>
              </td>
              <td class="px-2 py-2 text-sm border border-table-border text-right">{{ item.optimizedGmv }}</td>
              <!-- GMV Change Editable Column -->
              <td class="px-2 py-2 text-sm border border-table-border text-right">
                <template v-if="editColumn === 'gmvChange'">
                  <input
                    v-model.number="editValues[getRowKey(item)]"
                    class="border border-table-border rounded px-2 py-1 w-20"
                    type="number"
                  />
                </template>
                <template v-else>
                  {{ item.gmvChange }}
                </template>
              </td>
            </tr>
          </template>
        </template>
      </tbody>
    </table>
  </div>
</template>

<style scoped>
table {
  border-collapse: separate;
  border-spacing: 0;
}

th {
  position: sticky;
  top: 0;
  z-index: 10;
  transition: background-color 0.2s;
}

th:hover {
  /* background-color: #E2E8F0; */
}
</style>