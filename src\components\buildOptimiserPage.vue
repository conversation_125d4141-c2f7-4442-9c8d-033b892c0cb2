<template>
    <div class="">
        <!-- Step Navigation -->
        <div class="p-6 bg-white">
            <div class="flex w-full gap-2 bg-[#F9FAFB] p-2 rounded-lg overflow-x-auto">
                <div class="px-3 py-1 rounded cursor-pointer flex items-center gap-1" @click="goToHome">
                    <Home class="w-4 h-4" />
                </div>
                <template v-for="(step, index) in steps" :key="index">
                    <div class="px-2 py-1 rounded cursor-pointer flex items-center"
                        :class="{
                            'bg-[#16A34A] text-white': currentStep === index,
                            'bg-[#F3F4F6] text-gray-400 hover:cursor-no-drop': currentStep !== index && step.disabled,
                            
                        }"
                        @click="!step.disabled && (currentStep = index)">
                        {{ step.name }}
                    </div>
                    <span v-if="index < steps.length - 1" class="text-gray-400 text-sm flex items-center">></span>
                </template>
            </div>
        </div>

        <!-- Step Content -->
         <!-- <div class="w-full overflow-x-auto bg-[#F9FAFB] shadow rounded-lg"> -->
        <div class=" bg-[#F9FAFB] shadow rounded-lg overflow-auto">
            <component  :is="steps[currentStep].component"/>
        </div>

        <!-- Navigation -->
        <div class="flex justify-end mt-4">
            <button @click="goToNextStep"
                class="bg-tertiary text-white hover:bg-green-900 px-6 py-2 rounded "
                :disabled="currentStep === steps.length - 1">
                Next →
            </button>
        </div>
    </div>
</template>


<script setup>
import { ref } from 'vue'
import { Home } from 'lucide-vue-next'
import { useRouter } from 'vue-router'
import BusinessDashboard from './BusinessDashboard.vue'
import StoreCluster from './StoreCluster.vue'
import ControlStoreSelection from './ControlStoreSelection.vue'
import SpaceDataSummary from './SpaceDataSummary.vue'
import SpaceHealthDashboard from './SpaceHealthDashboard.vue'
import EvaluationDashboard from './EvaluationDashboard.vue'
import OptimizationSummary from './Optimization.vue'
import RangeBasedOptimisationSummary from './OptimizationSummary.vue'
import SaturationPoint from './ProductivityChartsDashboard.vue'
import OutlierHandle from './OutlierHandle.vue'
import PerformanceCalculation from './PerformanceCalculation.vue'


// Steps definition
const steps = [
    { name: 'Business Requirement', component: BusinessDashboard,disabled:false},
    { name: 'Store Clustering', component: StoreCluster,disabled:true},
    { name: 'Control & Test Store', component: ControlStoreSelection,disabled:true},
    { name: 'Outlier Handle', component: OutlierHandle,disabled:true},
    { name: 'Space Data Summary', component: SpaceDataSummary,disabled:true},
    { name: 'Space Health Dashboard', component: SpaceHealthDashboard,disabled:true},
    { name: 'Performance Calculation', component: PerformanceCalculation,disabled:true},
    { name: 'Range Based Optimisation Summary', component: RangeBasedOptimisationSummary,disabled:true },
    { name: 'Optimization Summary', component: OptimizationSummary ,disabled:true},
    { name: 'Productivity Charts', component: SaturationPoint ,disabled:true},
    { name: 'Evaluation', component: EvaluationDashboard ,disabled:true}
]

const currentStep = ref(0)
const router = useRouter()
// function goToNextStep() {
//     if (currentStep.value < steps.length - 1) {
//         currentStep.value++
//     }
// }
function goToNextStep() {
    if (currentStep.value < steps.length - 1) {
        // Enable the next step
        steps[currentStep.value + 1].disabled = false
        // Move to the next step
        currentStep.value++
    }
}
const goToHome = () => {
    router.push({ name: '/spaceoptimization/HomePage' })
}
</script>

<style scoped>
/* Optional scroll behavior */
::-webkit-scrollbar {
    height: 6px;
}

::-webkit-scrollbar-thumb {
    background-color: #ccc;
    border-radius: 4px;
}
</style>