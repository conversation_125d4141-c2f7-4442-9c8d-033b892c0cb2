<!-- ControlStoreComponent.vue -->
<script setup lang="ts">
/* ---------------------------------------------
 * imports & Vue helpers
 * -------------------------------------------*/
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'

/* ---------------------------------------------
 * 1.  Dummy master data
 * -------------------------------------------*/
interface Store { id: string; name: string; cluster: number }
interface ControlStoreSplit {
  id?: string
  name?: string
  cluster?: number
  [key: string]: string | number | undefined
}

const testStores: Store[] = [
  { id: 'T1', name: 'Test Store 1', cluster: 1 },
  { id: 'T2', name: 'Test Store 2', cluster: 2 },
  { id: 'T3', name: 'Test Store 3', cluster: 1 }
]

const controlStores: Store[] = [
  { id: 'C1', name: 'Control Store 1', cluster: 1 },
  { id: 'C2', name: 'Control Store 2', cluster: 2 },
  { id: 'C3', name: 'Control Store 3', cluster: 1 },
  { id: 'C4', name: 'Control Store 4', cluster: 2 }
]

/* ---------------------------------------------
 * 2.  Table-1 static headers & similarity rows
 * -------------------------------------------*/
const table1Headers = [
  'TEST STORE',
  'CONTROL STORE',
  'SIMILARITY SCORE',
  'PRIMARY CONTRIBUTOR',
  'TEST CLUSTER',
  'CONTROL CLUSTER'
]

const similarityRows = [
  { test: 'Test Store 1', control: 'Control Store 1', score: 0.85, contributor: 'Volume',   testCluster: 1, controlCluster: 1 },
  { test: 'Test Store 1', control: 'Control Store 2', score: 0.82, contributor: 'Nationality', testCluster: 1, controlCluster: 2 },
  { test: 'Test Store 2', control: 'Control Store 3', score: 0.88, contributor: 'Volume',   testCluster: 2, controlCluster: 1 },
  { test: 'Test Store 3', control: 'Control Store 4', score: 0.80, contributor: 'Nationality', testCluster: 1, controlCluster: 2 }
]

/* ---------------------------------------------
 * 3.  Reactive state
 * -------------------------------------------*/
const assignments          = ref<Record<string, string[]>>({})   // { testId: [controlIds] }
const expandedRows         = ref<string[]>([])                   // which test rows are expanded
const selectedTestStore    = ref<string>('')                     // v-model for test dropdown
const selectedControlStores= ref<string[]>([])                   // v-model for multiselect
const showControlDropdown  = ref(false)                          // toggle multiselect list
const controlDropdownRef   = ref<HTMLElement | null>(null)       // for click-outside close

/* confirmation-dialog state  */
const showDialog           = ref(false)
const dialogMsg            = ref('')
const pendingTestStore     = ref<string>('')                     // temp cache before OK
const pendingControlStores = ref<string[]>([])

/* ---------------------------------------------
 * 4.  Column names for Table-2
 * -------------------------------------------*/
const nationalityCols = ['Nationals','Arab Expats','ISC','SEAC','Western','Unspecified']
const groupVolumeCols = ['Kitchen','Bedroom','Tabletop','Dining','Decor','Gallery']

/* ---------------------------------------------
 * 5.  Derived rows for Table-2
 * -------------------------------------------*/
const assignmentData = computed(() => {
  /* Always render every test store; attach its current controls */
  return testStores.map(test => {
    const testSplit: ControlStoreSplit = {           // dummy % values for test
      Nationals:25,'Arab Expats':20,ISC:15,SEAC:10,Western:12,Unspecified:8,
      Kitchen:18, Bedroom:16, Tabletop:11,Dining:13,Decor:9, Gallery:7
    }
    const controls = (assignments.value[test.id] || []).map(cid => {
      const c = controlStores.find(cs => cs.id === cid)!
      return {
        ...c,
        Nationals:20,'Arab Expats':15,ISC:10,SEAC:5,Western:8,Unspecified:2,
        Kitchen:12, Bedroom:14, Tabletop:9, Dining:11, Decor:7, Gallery:6
      } as ControlStoreSplit
    })
    return { ...test, split: testSplit, controls }
  })
})

/* ---------------------------------------------
 * 6.  Utility helpers
 * -------------------------------------------*/
const percent = (v: string|number|undefined) =>
  v===undefined||v===null||v==='' ? '-%' : `${v}%`

/* ---------------------------------------------
 * 7.  Dropdown handlers
 * -------------------------------------------*/
function handleTestStoreChange (e: Event) {
  const id = (e.target as HTMLSelectElement).value
  selectedTestStore.value      = id
  selectedControlStores.value  = assignments.value[id] ? [...assignments.value[id]] : []
}
function toggleControlDropdown() {
  showControlDropdown.value = !showControlDropdown.value
}
function handleControlCheckboxChange(id:string) {
  const idx = selectedControlStores.value.indexOf(id)
  idx>-1 ? selectedControlStores.value.splice(idx,1)
         : selectedControlStores.value.push(id)
}

/* click-outside close for multiselect list */
function handleClickOutside(e:MouseEvent){
  if(showControlDropdown.value && controlDropdownRef.value &&
     !controlDropdownRef.value.contains(e.target as Node)){
    showControlDropdown.value=false
  }
}
onMounted(()=>document.addEventListener('mousedown',handleClickOutside))
onUnmounted(()=>document.removeEventListener('mousedown',handleClickOutside))

/* ---------------------------------------------
 * 8.  Add Control Store BTN → open dialog only
 * -------------------------------------------*/
function handleAddControlStore(){
  if(!selectedTestStore.value || selectedControlStores.value.length===0) return
  pendingTestStore.value     = selectedTestStore.value
  pendingControlStores.value = [...selectedControlStores.value]

  /* Build confirmation text */
  const storeNames = pendingControlStores.value
    .map(id=>controlStores.find(cs=>cs.id===id)?.name)
    .filter(Boolean)
    .join(', ')
  const testName = testStores.find(ts=>ts.id===pendingTestStore.value)?.name
  dialogMsg.value = `${storeNames} added to ${testName} successfully.`
  showDialog.value = true
}

/* ---------------------------------------------
 * 9.  OK in dialog → commit & expand
 * -------------------------------------------*/
async function handleDialogOk(){
  /* commit */
  assignments.value[pendingTestStore.value] = [...pendingControlStores.value]

  /* expand only that parent row */
  expandedRows.value = [pendingTestStore.value]

  /* reset dropdowns & UI */
  selectedTestStore.value    = ''
  selectedControlStores.value= []
  showControlDropdown.value  = false
  showDialog.value           = false

  /* clear pending cache */
  pendingTestStore.value     = ''
  pendingControlStores.value = []

  /* optional: scroll row into view */
  await nextTick()
  document.querySelector(`[data-row='${expandedRows.value[0]}']`)
          ?.scrollIntoView({behavior:'smooth',block:'center'})
}

/* ---------------------------------------------
 * 10.  Expand / collapse parent row
 * -------------------------------------------*/
function toggleExpand(id:string, hasChildren:boolean){
  if(!hasChildren) return
  if(expandedRows.value.includes(id)){
    expandedRows.value = expandedRows.value.filter(r=>r!==id)
  }else{
    expandedRows.value.push(id)
  }
}

/* keep control selection in sync when user flips tests */
watch(selectedTestStore,(id)=>{
  selectedControlStores.value = id && assignments.value[id]
    ? [...assignments.value[id]]
    : []
})
</script>

<template>
  <div class="flex w-full">
    <!-- Main panel -->
    <div class="flex-1 flex flex-col">
      <!-- Body -->
      <main class="flex-1 p-4 sm:p-6 lg:p-8">
        <div class="bg-card-bg rounded-lg shadow p-6">

          <!-- TABLE-1 : similarity list -->
          <div class="mb-8">
            <div class="font-bold text-xl text-tertiary mb-2">Test & Control Store List</div>
            <table class="w-full rounded-lg">
              <thead>
                <tr class="bg-table-header">
                  <th v-for="h in table1Headers" :key="h" class="font-semibold text-sm border border-table-border text-center">{{ h }}</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="row in similarityRows" :key="row.test+row.control" class="even:bg-table-row-hover">
                  <td class="text-sm font-medium  border border-table-border text-center">{{row.test}}</td>
                  <td class="text-sm font-medium  border border-table-border text-center">{{row.control}}</td>
                  <td class="text-sm font-medium  border border-table-border text-center">{{row.score.toFixed(2)}}</td>
                  <td class="text-sm font-medium  border border-table-border text-center">{{row.contributor}}</td>
                  <td class="text-sm font-medium  border border-table-border text-center">{{row.testCluster}}</td>
                  <td class="text-sm font-medium  border border-table-border text-center">{{row.controlCluster}}</td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- DROPDOWNS -->
          <div class="flex flex-row gap-8 items-end mb-8">
            <!-- Test Store -->
            <div class="flex flex-col gap-2">
              <label class="font-semibold text-sm ">Test Store</label>
              <div class="relative">
                <select v-model="selectedTestStore"
                        @change="handleTestStoreChange"
                        class="min-w-[220px] min-h-[38px] border border-table-border rounded bg-card-bg px-2 py-1 text-sm font-medium pr-8 focus:border-secondary focus:ring-secondary">
                  <option value="" disabled class="text-table-border">Select test store</option>
                  <option v-for="s in testStores" :key="s.id" :value="s.id">{{ s.name }}</option>
                </select>
              </div>
            </div>

            <!-- Control Store multiselect -->
            <div class="flex flex-col gap-2">
              <label class="font-semibold text-sm ">Control Store</label>
              <div class="relative" ref="controlDropdownRef">
                <div class="min-w-[220px] min-h-[38px] border border-table-border rounded bg-card-bg px-2 py-1 text-sm font-medium pr-8 flex items-center justify-between cursor-pointer"
                     @click="toggleControlDropdown">
                  <span v-if="selectedControlStores.length===0" class="text-table-border">Select control stores</span>
                  <span v-else class="text-tertiary">{{ selectedControlStores.length }} selected</span>
                </div>
                <div v-if="showControlDropdown" class="absolute z-10 mt-1 w-full bg-card-bg border border-table-border rounded shadow max-h-56 overflow-y-auto">
                  <div v-for="s in controlStores" :key="s.id" class="px-4 py-2 hover:bg-table-row-hover cursor-pointer">
                    <label class="flex items-center gap-2 cursor-pointer text-sm font-medium">
                      <input type="checkbox"
                             :checked="selectedControlStores.includes(s.id)"
                             @change="handleControlCheckboxChange(s.id)"
                             class="accent-secondary" />
                      <span class="">{{ s.name }}</span>
                    </label>
                  </div>
                </div>
              </div>
            </div>

            <!-- Add Control Store BTN -->
            <button class="ml-6 px-5 py-2 rounded bg-secondary text-card-bg font-semibold text-sm shadow hover:bg-tertiary transition disabled:opacity-50 disabled:cursor-not-allowed self-end"
                    :disabled="!selectedTestStore || selectedControlStores.length===0"
                    @click="handleAddControlStore">
              Add Control Store
            </button>
          </div>

          <!-- CONFIRMATION DIALOG -->
          <div v-if="showDialog"
               class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-40">
            <div class="bg-white rounded shadow-lg px-8 py-6 relative flex flex-col items-center border-t-4 border-secondary">
              <div class="text-xl font-bold mb-2 mt-2">Success!</div>
              <div class=" mb-6 text-center">{{ dialogMsg }}</div>
              <button class="px-6 py-2 rounded bg-secondary text-card-bg font-semibold text-sm hover:bg-tertiary transition" @click="handleDialogOk">
                OK
              </button>
            </div>
          </div>

          <!-- TABLE-2 : comparison metrics -->
          <div class="mb-4 font-bold text-xl text-tertiary">Store Comparison Metrics</div>
          <div class="overflow-x-auto w-full">
            <table class="w-full rounded-lg">
              <!-- top header -->
              <thead>
                <tr class="bg-table-header">
                  <th class="sticky-col font-semibold text-sm border border-table-border text-center">Store</th>
                  <th colspan="6"   class="font-semibold text-sm border border-table-border text-center">Nationality Split</th>
                  <th colspan="6"   class="font-semibold text-sm border border-table-border text-center">Group Volume Split</th>
                </tr>
                <tr class="bg-table-row-hover">
                  <th class="sticky-col border border-table-border text-center"></th>
                  <th v-for="c in nationalityCols" :key="c" class="font-semibold text-sm border border-table-border text-center">{{c}}</th>
                  <th v-for="c in groupVolumeCols"  :key="c" class="font-semibold text-sm border border-table-border text-center">{{c}}</th>
                </tr>
              </thead>

              <!-- data rows -->
              <tbody>
                <template v-for="test in assignmentData" :key="test.id">
                  <!-- parent row -->
                  <tr class="parent-row"
                      :data-row="test.id">
                    <td class="sticky-col text-sm font-medium border border-table-border bg-table-row-hover text-center">{{ test.name }}</td>
                    <td v-for="c in nationalityCols" :key="c" class="text-sm font-medium border border-table-border text-center">{{percent(test.split[c])}}</td>
                    <td v-for="c in groupVolumeCols"  :key="c" class="text-sm font-medium border border-table-border text-center">{{percent(test.split[c])}}</td>
                  </tr>

                  <!-- child rows (render only if expanded) -->
                  <tr v-if="expandedRows.includes(test.id)"
                      v-for="ctrl in test.controls"
                      :key="ctrl.id"
                      class="child-row">
                    <td class="sticky-col child-indent text-sm font-medium border border-table-border bg-table-row-hover text-center">{{ ctrl.name }}</td>
                    <td v-for="c in nationalityCols" :key="c" class="text-sm font-medium border border-table-border text-center">{{ percent(ctrl[c]) }}</td>
                    <td v-for="c in groupVolumeCols"  :key="c" class="text-sm font-medium border border-table-border text-center">{{ percent(ctrl[c]) }}</td>
                  </tr>
                </template>
              </tbody>
            </table>
          </div>

        </div>
      </main>
    </div>
  </div>
</template>

<style scoped>
.dropdowns-inline {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  gap: 32px;
  margin-bottom: 2rem;
}
.dropdown-stack {
  display: flex;
  flex-direction: column;
  gap: 6px;
}
.sticky-col {
  position: sticky;
  left: 0;
  background: #f0fdf4;
  z-index: 2;
}
.child-row {
  background: #f0fdf4;
}
.child-indent {
  padding-left: 32px;
}
.chevron {
  display: inline-block;
  vertical-align: middle;
  margin-right: 4px;
  transition: transform 0.2s;
}
</style> 