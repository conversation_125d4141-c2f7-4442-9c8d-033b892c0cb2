<template>
</template>
<script setup lang="ts">
import axios from 'axios';
import { onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import {redirectUri} from '../main';

const route = useRoute();
const router = useRouter();

onMounted(async () => {
  const code = route.query.code;

  if (!code) {
    console.error('No code found in query');
    return;
  }

  try {
    const response = await axios.post('auth/login/', {
      code,
      redirect_uri: redirectUri
    });

    const tokenData = response.data;

    // Store tokens in localStorage
    localStorage.setItem('access_token', tokenData.access_token);
    localStorage.setItem('refresh_token', tokenData.refresh_token);

    if (tokenData.user) {
      localStorage.setItem('user', JSON.stringify(tokenData.user.name));
      localStorage.setItem('user', JSON.stringify(tokenData.user.email));

    }

    // Redirect to landing page
    router.push({ name: 'concept-selection' });

  } catch (error) {
    console.error('Token exchange failed:', error.response?.data || error.message);
  }
});
</script>
