<template>
    <div class="p-5 space-y-3">
        <!-- Header -->
        <div class="flex items-center justify-between">
            <div class="flex items-center gap-4">
                <label class="flex items-center gap-2 cursor-pointer">
                    <div class="w-4 h-4 bg-red-600 rounded-sm border border-black"></div>
                    <input type="radio" v-model="outlierType" value="major" class="sr-only" />
                    <span class="text-sm">Major Outliers</span>
                </label>
                <label class="flex items-center gap-2 cursor-pointer">
                    <div class="w-4 h-4 bg-blue-600 border border-black"></div>
                    <input type="radio" v-model="outlierType" value="minor" class="sr-only" />
                    <span class="text-sm">Minor Outliers</span>
                </label>
            </div>
            <div>
                <button class="top-2 right-2 text-xl text-gray-500 hover:text-black" @click="$emit('close')">
                    ✖
                </button>
            </div>
        </div>

        <!-- Subclass Dropdown -->
        <div class="w-1/4">
            <label class="text-sm font-semibold">Subclass</label>
            <Multiselect v-model="selectedSubclass" :options="subclassOptions" :searchable="true"
                placeholder="Select Subclass" label="label" track-by="value" class="mt-1" />
        </div>

        <!-- Chart -->
        <div class="bg-white rounded-xl shadow p-4 h-[28vw]">
            <canvas id="scatterChart" height="200"></canvas>
        </div>

        <!-- Outlier Summary -->
        <div class="text-sm italic text-red-600 pt-2">
            {{ outlierSummary }}
        </div>
    </div>
</template>
  
  <script setup>
  import { ref, watch, onMounted } from 'vue'
  import Chart from 'chart.js/auto'
  import Multiselect from 'vue-multiselect'
  
  let chartInstance = null
  
  const selectedSubclass = ref(null)
  const outlierType = ref('major')
  const outlierSummary = ref('')
  
  const allData = [
  { subclass: 'CUSHIONS', store: '29004-Sep', x: 60, y: 12000, outlier: 'minor' },
  { subclass: 'CUSHIONS', store: '29001-Sep', x: 62, y: 11800, outlier: 'minor' },
  { subclass: 'CUSHIONS', store: '29003-Oct', x: 98, y: 12500, outlier: 'major' },
  { subclass: 'CUSHIONS', store: '29001-Oct', x: 90, y: 3000, outlier: 'major' },
  { subclass: 'CUSHIONS', store: '29002-Aug', x: 67, y: 6000, outlier: 'normal' },
  { subclass: 'CUSHIONS', store: '29003-Jul', x: 78, y: 10000, outlier: 'normal' },
  { subclass: 'CLOCKS', store: '29002-Oct', x: 65, y: 4500, outlier: 'major' },
  { subclass: 'CLOCKS', store: '29001-Sep', x: 75, y: 11000, outlier: 'minor' },
  { subclass: 'CLOCKS', store: '29004-Jul', x: 70, y: 9500, outlier: 'normal' }
]

  const subclassOptions = [...new Set(allData.map(d => d.subclass))].map(d => ({
    label: d,
    value: d
  }))
  
  // Filter and redraw chart
  const drawChart = () => {
    const subclass = selectedSubclass.value?.value || subclassOptions[0].value;
    const type = outlierType.value;

    // Always show all points for the selected subclass
    const filteredData = allData.filter(d => d.subclass === subclass);

    if (chartInstance) {
      chartInstance.destroy();
    }

    const ctx = document.getElementById('scatterChart');
    chartInstance = new Chart(ctx, {
      type: 'scatter',
      data: {
        datasets: [{
          label: `GMV/Day vs Linear Meter for ${subclass}`,
          data: filteredData.map(d => ({ x: d.x, y: d.y, store: d.store })),
          pointBackgroundColor: filteredData.map(d =>
            d.outlier === type
              ? (type === 'major' ? 'red' : 'blue')
              : 'gray'
          ),
          pointRadius: filteredData.map(d =>
            d.outlier === type ? 7 : 5
          ),
          pointHoverRadius: 8
        }]
      },
      options: {
        plugins: {
          tooltip: {
            callbacks: {
              label: context => {
                const point = filteredData[context.dataIndex];
                return `${point.store}: GMV/Day ${point.y}, Linear Meter ${point.x}`;
              }
            }
          },
          title: {
            display: true,
            text: `GMV/Day vs Linear Meter (Across Store-Month) for ${subclass}`,
            font: { size: 14 }
          }
        },
        scales: {
          x: {
            title: { display: true, text: 'Linear Meter' }
          },
          y: {
            title: { display: true, text: 'GMV/Day' }
          }
        }
      }
    });

    // Count only selected outlier type for summary
    const outlierCount = filteredData.filter(d => d.outlier === type).length;
    outlierSummary.value = `${outlierCount} ${type === 'major' ? 'Major' : 'Minor'} Outliers found in ${subclass}`;
  };
  
  onMounted(() => {
    if (!selectedSubclass.value) {
      selectedSubclass.value = subclassOptions[0]
    }
    drawChart()
  })
  
  watch([selectedSubclass, outlierType], drawChart)
  </script>
  
  <style scoped>
  @import "vue-multiselect/dist/vue-multiselect.css";
  
  canvas {
    max-width: 100%;
  }
  </style>
