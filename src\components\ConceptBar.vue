<template>
  <div class="flex w-full justify-end">
    <div class="flex-1 flex flex-col overflow-hidden">
      <!-- Top Bar -->
      <div class="bg-white flex justify-end pr-8 pt-2 pb-2">
        <!-- User and Concept Info -->
        <div class="flex items-center">
          <span v-if="username" class="flex font-semibold text-base mr-4">Hi, {{ username }}</span>
          <div v-if="conceptObj" class="flex items-center">
            <img
              :src="conceptObj.img"
              :alt="conceptObj.name"
              class="h-8 w-8 object-contain bg-white rounded-full bg-primary border border-tertiary mr-2"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { inject, computed } from 'vue';
const baseImgUrl = inject('baseImageUrl');

const username = computed(() => localStorage.getItem('user') || '');
const conceptId = computed(() => Number(localStorage.getItem('concept')) || null);

const concepts = [
  { name: 'Babyshop', key: 1, img: `${baseImgUrl}/babyshop.png` },
  { name: 'Homebox', key: 2, img: `${baseImgUrl}/homebox.png` },
  { name: 'Lifestyle', key: 3, img: `${baseImgUrl}/lifestyle.png` },
  { name: 'Splash', key: 4, img: `${baseImgUrl}/splash.png` }
];

const conceptObj = computed(() => concepts.find(c => c.key === conceptId.value) || null);
</script>