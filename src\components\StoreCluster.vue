<template>
  <div class=" w-full flex flex-col">

    <!-- Main Content -->
    <main class="flex-1 overflow-y-auto p-4 sm:p-6 lg:p-8">
      <div class="bg-primary/30 rounded-lg shadow p-4 overflow-x-auto">
        <table class="min-w-full text-sm border border-tertiary text-left bg-white rounded-xl overflow-hidden">
          <thead class="bg-primary">
            <tr>
              <th class="p-3 font-semibold">Cluster</th>
              <th class="p-3 font-semibold">Store ID</th>
              <th class="p-3 font-semibold">Storename</th>
              <th class="p-3 font-semibold">Revenue / Sq.ft</th>
              <th class="p-3 font-semibold">Area Sqft</th>
              <th class="p-3 font-semibold">% Nationals</th>
              <th class="p-3 font-semibold">% Arab Expats</th>
              <th class="p-3 font-semibold">% ISC</th>
              <th class="p-3 font-semibold">% SEA</th>
              <th class="p-3 font-semibold">% Western</th>
              <th class="p-3 font-semibold">% Unspecified</th>
            </tr>
          </thead>

          <tbody>
            <template v-for="(cluster, index) in clusters" :key="index">
              <!-- Store Rows -->
              <tr
                v-for="(store, sIdx) in cluster.stores"
                v-show="expandedClusters[index]"
                :key="sIdx"
                :class="[
                  'transition border-b last:border-b-0 bg-white'
                ]"
              >
                <td class="p-3">{{ index + 1 }}</td>
                <td class="p-3">{{ store.store_id }}</td>
                <td class="p-3">{{ store.storename }}</td>
                <td class="p-3 text-right">{{ store.revenue }}</td>
                <td class="p-3 text-right">{{ store.area_sqft }}</td>
                <td class="p-3 text-center" :class="dynamicBgColor('nationals', store.nationals)">
                  {{ store.nationals }}%
                </td>
                <td class="p-3 text-center" :class="dynamicBgColor('arabExpats', store.arabExpats)">
                  {{ store.arabExpats }}%
                </td>
                <td class="p-3 text-center" :class="dynamicBgColor('isc', store.isc)">
                  {{ store.isc }}%
                </td>
                <td class="p-3 text-center" :class="dynamicBgColor('sea', store.sea)">
                  {{ store.sea }}%
                </td>
                <td class="p-3 text-center" :class="dynamicBgColor('western', store.western)">
                  {{ store.western }}%
                </td>
                <td class="p-3 text-center" :class="dynamicBgColor('unspecified', store.unspecified)">
                  {{ store.unspecified }}%
                </td>
              </tr>
              <!-- Total Row (now at the bottom of each cluster) -->
              <tr
                :class="[
                  'font-semibold cursor-pointer transition',
                  expandedClusters[index] ? 'bg-secondary/10 hover:bg-secondary/20' : 'bg-white'
                ]"
                @click="toggleCluster(index)"
              >
                <td class="p-3">
                  <svg
                    :class="{ 'rotate-90': expandedClusters[index] }"
                    class="inline-block w-4 h-4 transform transition-transform duration-200 mr-1"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 5l7 7-7 7"
                    />
                  </svg>
                  {{ index + 1 }} Total
                </td>
                <td colspan="2" class="text-center">----</td>
                <td class="p-3 text-right">{{ cluster.totalRevenue }}</td>
                <td class="p-3 text-right">{{ cluster.totalArea }}</td>
                <td class="p-3 text-center">{{ cluster.avgNationals }}%</td>
                <td class="p-3 text-center">{{ cluster.avgArabExpats }}%</td>
                <td class="p-3 text-center">{{ cluster.avgISC }}%</td>
                <td class="p-3 text-center">{{ cluster.avgSEA }}%</td>
                <td class="p-3 text-center">{{ cluster.avgWestern }}%</td>
                <td class="p-3 text-center">{{ cluster.avgUnspecified }}%</td>
              </tr>
            </template>
          </tbody>
        </table>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const clusters = ref([
  {
    stores: [
      {
        store_id: '29001',
        storename: 'HB - Ghubiab',
        revenue: 1374,
        area_sqft: 24027,
        nationals: 21,
        arabExpats: 22,
        isc: 12,
        sea: 16,
        western: 3,
        unspecified: 12
      },
      {
        store_id: '29027',
        storename: 'HB Sky Garden',
        revenue: 583,
        area_sqft: 25833,
        nationals: 3,
        arabExpats: 60,
        isc: 15,
        sea: 2,
        western: 5,
        unspecified: 15,
      },
      {
        store_id: '29028',
        storename: 'HB Marina',
        revenue: 900,
        area_sqft: 20000,
        nationals: 25,
        arabExpats: 30,
        isc: 25,
        sea: 10,
        western: 7,
        unspecified: 8
      },
      {
        store_id: '29029',
        storename: 'HB Downtown',
        revenue: 1100,
        area_sqft: 22000,
        nationals: 18,
        arabExpats: 40,
        isc: 10,
        sea: 8,
        western: 6,
        unspecified: 18
      }
    ],
    totalRevenue: 989,
    totalArea: 28433,
    avgNationals: 16,
    avgArabExpats: 38,
    avgISC: 15,
    avgSEA: 9,
    avgWestern: 5,
    avgUnspecified: 13
  },
  {
    stores: [
      {
        store_id: '29051',
        storename: 'HB - Dana Plaza Fujairah',
        revenue: 455,
        area_sqft: 35561,
        nationals: 67,
        arabExpats: 13,
        isc: 8,
        sea: 1,
        western: 3,
        unspecified: 8
      },
      {
        store_id: '29052',
        storename: 'HB - Mall of Emirates',
        revenue: 1200,
        area_sqft: 40000,
        nationals: 40,
        arabExpats: 20,
        isc: 20,
        sea: 10,
        western: 5,
        unspecified: 5
      },
      {
        store_id: '29053',
        storename: 'HB - City Centre',
        revenue: 980,
        area_sqft: 32000,
        nationals: 30,
        arabExpats: 25,
        isc: 18,
        sea: 12,
        western: 8,
        unspecified: 7
      }
    ],
    totalRevenue: 878,
    totalArea: 25806,
    avgNationals: 46,
    avgArabExpats: 19,
    avgISC: 15,
    avgSEA: 7,
    avgWestern: 5,
    avgUnspecified: 7
  },
  {
    stores: [
      {
        store_id: '29061',
        storename: 'HB - Al Wahda',
        revenue: 700,
        area_sqft: 18000,
        nationals: 10,
        arabExpats: 35,
        isc: 22,
        sea: 15,
        western: 9,
        unspecified: 9
      },
      {
        store_id: '29062',
        storename: 'HB - Yas Mall',
        revenue: 1500,
        area_sqft: 50000,
        nationals: 55,
        arabExpats: 18,
        isc: 12,
        sea: 7,
        western: 4,
        unspecified: 4
      }
    ],
    totalRevenue: 1100,
    totalArea: 34000,
    avgNationals: 32,
    avgArabExpats: 26,
    avgISC: 17,
    avgSEA: 11,
    avgWestern: 6,
    avgUnspecified: 6
  }
])

const expandedClusters = ref(clusters.value.map(() => false))

const toggleCluster = (index) => {
  expandedClusters.value[index] = !expandedClusters.value[index]
}

// Helper to get all values for a column across all clusters/stores
function getAllValuesForKey(key) {
  return clusters.value.flatMap(cluster =>
    cluster.stores.map(store => store[key]).filter(v => typeof v === 'number')
  )
}

// Compute min/max for each column
const columnStats = computed(() => {
  const keys = ['nationals', 'arabExpats', 'isc', 'sea', 'western', 'unspecified']
  const stats = {}
  keys.forEach(key => {
    const values = getAllValuesForKey(key)
    stats[key] = {
      min: Math.min(...values),
      max: Math.max(...values)
    }
  })
  return stats
})

// Dynamic color mapping based on percentile
function dynamicBgColor(key, value) {
  const { min, max } = columnStats.value[key]
  if (max === min) return 'bg-primary' // fallback if all values are same
  const percent = (value - min) / (max - min)
  if (percent >= 0.66) return ' font-semibold bg-green-100'
  if (percent >= 0.33) return ' font-semibold bg-yellow-100'
  return ' font-semibold bg-red-300'
}
</script>

<style scoped>
th,
td {
  border: 1px solid #3A3A3A;
}
</style>
