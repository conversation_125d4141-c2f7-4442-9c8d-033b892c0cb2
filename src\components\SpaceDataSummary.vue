<template>
  <div class="p-6 space-y-8">
    <!-- Filters -->
    <div class="flex flex-wrap gap-4 items-start mb-6">
      <div class="w-48">
        <label class="text-sm font-semibold">Store</label>
        <Multiselect v-model="selectedStore" :options="storeOptions" :searchable="true" placeholder="Select Store"
          label="label" track-by="value" :multiple="false" />
      </div>
      <div class="w-48">
        <label class="text-sm font-semibold">Group</label>
        <Multiselect v-model="selectedGroups" :options="dynamicGroupOptions" :searchable="true"
          placeholder="Select Groups" label="label" track-by="value" :multiple="true" />
      </div>
      <div class="w-48">
        <label class="text-sm font-semibold">Department</label>
        <Multiselect v-model="selectedDepartments" :options="dynamicDepartmentOptions" :searchable="true"
          placeholder="Select Departments" label="label" track-by="value" :multiple="true" />
      </div>
      <div class="w-48">
        <label class="text-sm font-semibold">Class</label>
        <Multiselect v-model="selectedClasses" :options="dynamicClassOptions" :searchable="true"
          placeholder="Select Classes" label="label" track-by="value" :multiple="true" />
      </div>
      <div class="w-48">
        <label class="text-sm font-semibold">Subclass</label>
        <Multiselect v-model="selectedSubclasses" :options="dynamicSubclassOptions" :searchable="true"
          placeholder="Select Subclasses" label="label" track-by="value" :multiple="true" />
      </div>
      <div class="w-48">
        <label class="text-sm font-semibold">From Year</label>
        <select v-model="fromYear" class="mt-1 block w-full border rounded px-3 py-2 text-sm">
          <option v-for="y in yearOptions" :key="y" :value="y">{{ y }}</option>
        </select>
      </div>
      <div class="w-48">
        <label class="text-sm font-semibold">From Month</label>
        <select v-model="fromMonth" class="py-2 mt-1 block w-full border rounded px-3 py-1 text-sm">
          <option v-for="m in monthOptions" :key="m.value" :value="m.value">{{ m.label }}</option>
        </select>
      </div>
      <div class="w-48">
        <label class="text-sm font-semibold">To Year</label>
        <select v-model="toYear" class="py-2 mt-1 block w-full border rounded px-3 py-1 text-sm">
          <option v-for="y in yearOptions" :key="y" :value="y">{{ y }}</option>
        </select>
      </div>
      <div class="w-48">
        <label class="text-sm font-semibold">To Month</label>
        <select v-model="toMonth" class="py-2 mt-1 block w-full border rounded px-3 py-1 text-sm">
          <option v-for="m in monthOptions" :key="m.value" :value="m.value">{{ m.label }}</option>
        </select>
      </div>
      <div class="flex items-end">
        <button @click="applyFilters"
          class="bg-gradient-to-r from-secondary/80 to-tertiary text-white px-4 py-1 mt-7 rounded font-semibold shadow hover:from-tertiary hover:to-secondary/80 transition">
          Filter
        </button>
      </div>
    </div>

    <!-- Main Chart -->
    <div v-if="hasData">
      <div class="flex justify-center">
        <div class="bg-white rounded-xl shadow p-4 w-[70vw] flex flex-col items-center">
          <canvas id="lineChart" height="150"></canvas>
        </div>
      </div>

      <!-- Metric Dropdown and Bar Charts -->
      <div class="flex flex-col items-center">
        <div class="flex items-center gap-4 mb-4 mt-3">
          <label class="text-sm font-semibold">Metric</label>
          <Multiselect v-model="selectedMetric" :options="metricOptions" :searchable="true" placeholder="Select Metric"
            label="label" track-by="value" />
        </div>
        <div class="grid grid-cols-2 gap-6">
          <div class="bg-white rounded-xl shadow p-4 w-[30vw] flex flex-col items-center">
            <canvas id="top5Chart" height="200"></canvas>
          </div>
          <div class="bg-white rounded-xl shadow p-4 w-[30vw] flex flex-col items-center">
            <canvas id="bottom5Chart" height="200"></canvas>
          </div>
        </div>
      </div>
    </div>
    <div v-else class="text-center text-gray-500 py-8">
      No data available for the selected filters.
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed, nextTick } from 'vue'
import Multiselect from 'vue-multiselect'
import Chart from 'chart.js/auto'

// Filter model states
const storeOptions = [29001, 29002, 29003, 29004, 29005, 29006, 29007, 29008, 29009, 29010, 29011, 29012, 29013, 29014].map(store => ({ label: `Store ${store}`, value: store }))
const selectedStore = ref(storeOptions[0]) // Default: first store selected
const selectedGroups = ref([])
const selectedDepartments = ref([])
const selectedClasses = ref([])
const selectedSubclasses = ref([])

// Get current date
const now = new Date()
// Calculate 9 months ago
const nineMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 8, 1) // -8 because current month is included

const fromYear = ref(nineMonthsAgo.getFullYear())
const fromMonth = ref(nineMonthsAgo.getMonth() + 1) // JS months are 0-based
const toYear = ref(now.getFullYear())
const toMonth = ref(now.getMonth() + 1)

const selectedMetric = ref({ label: 'GMV', value: 'GMV' })
const filtered = ref([])

const yearOptions = [2024, 2025]
const monthOptions = [
  { label: 'Jan', value: 1 }, { label: 'Feb', value: 2 }, { label: 'Mar', value: 3 }, { label: 'Apr', value: 4 },
  { label: 'May', value: 5 }, { label: 'Jun', value: 6 }, { label: 'Jul', value: 7 }, { label: 'Aug', value: 8 },
  { label: 'Sep', value: 9 }, { label: 'Oct', value: 10 }, { label: 'Nov', value: 11 }, { label: 'Dec', value: 12 }
]

const metricOptions = [
  { label: 'GMV', value: 'GMV' },
  { label: 'GMV/Day', value: 'GMVDay' },
  { label: 'Linear Meter (LM)', value: 'LM' },
  { label: 'Productivity', value: 'productivity' },
  { label: 'Contribution %', value: 'contribution' },
  { label: 'GMV Growth %', value: 'GMVGrowth' },
  { label: 'Footfall', value: 'footfall' },
  { label: 'Sales/Transaction Count', value: 'salesCount' },
  { label: 'Inventory Turnover', value: 'inventoryTurnover' }
]

// Example dataset for all combinations (keep your full rawData here)
const rawData = [
  {
    store: '29002', group: 'Group A', department: 'Dept X', class: 'Class 1', subclass: 'Duvet and Pillows',
    year: 2024, month: 10, linearMeter: 55, productivity: 450, GMV: 8000, GMVDay: 300, LM: 55, contribution: 20, GMVGrowth: 5, footfall: 1000, salesCount: 200, inventoryTurnover: 2.5
  },
  {
    store: '29002', group: 'Group A', department: 'Dept X', class: 'Class 1', subclass: 'Bedsheets',
    year: 2024, month: 10, linearMeter: 60, productivity: 400, GMV: 7000, GMVDay: 250, LM: 60, contribution: 18, GMVGrowth: 4, footfall: 900, salesCount: 180, inventoryTurnover: 2.2
  },
  {
    store: '29002', group: 'Group B', department: 'Dept Y', class: 'Class 1', subclass: 'Curtains',
    year: 2024, month: 10, linearMeter: 70, productivity: 420, GMV: 7500, GMVDay: 270, LM: 70, contribution: 19, GMVGrowth: 4.5, footfall: 950, salesCount: 190, inventoryTurnover: 2.3
  },
  {
    store: '29002', group: 'Group B', department: 'Dept Y', class: 'Class 1', subclass: 'Cushions',
    year: 2024, month: 10, linearMeter: 65, productivity: 430, GMV: 7200, GMVDay: 260, LM: 65, contribution: 17, GMVGrowth: 3.8, footfall: 920, salesCount: 185, inventoryTurnover: 2.1
  },
  {
    store: '29002', group: 'Group A', department: 'Dept X', class: 'Class 1', subclass: 'Blankets',
    year: 2024, month: 10, linearMeter: 68, productivity: 410, GMV: 7100, GMVDay: 240, LM: 68, contribution: 16, GMVGrowth: 3.5, footfall: 910, salesCount: 175, inventoryTurnover: 2.0
  },
  {
    store: '29002', group: 'Group A', department: 'Dept X', class: 'Class 1', subclass: 'Mattress Protectors',
    year: 2024, month: 10, linearMeter: 62, productivity: 415, GMV: 7300, GMVDay: 255, LM: 62, contribution: 15, GMVGrowth: 3.2, footfall: 905, salesCount: 170, inventoryTurnover: 1.9
  },
  { store: '29001', group: 'Group A', department: 'Dept X', class: 'Class 1', subclass: 'Duvet and Pillows', year: 2024, month: 10, linearMeter: 55, productivity: 450, GMV: 8000, GMVDay: 300, LM: 55, contribution: 20, GMVGrowth: 5, footfall: 1000, salesCount: 200, inventoryTurnover: 2.5 },
  { store: '29001', group: 'Group A', department: 'Dept X', class: 'Class 1', subclass: 'Bedsheets', year: 2024, month: 10, linearMeter: 60, productivity: 400, GMV: 7000, GMVDay: 250, LM: 60, contribution: 18, GMVGrowth: 4, footfall: 900, salesCount: 180, inventoryTurnover: 2.2 },
  { store: '29001', group: 'Group B', department: 'Dept Y', class: 'Class 1', subclass: 'Curtains', year: 2024, month: 10, linearMeter: 70, productivity: 420, GMV: 7500, GMVDay: 270, LM: 70, contribution: 19, GMVGrowth: 4.5, footfall: 950, salesCount: 190, inventoryTurnover: 2.3 },
  { store: '29001', group: 'Group B', department: 'Dept Y', class: 'Class 1', subclass: 'Cushions', year: 2024, month: 10, linearMeter: 65, productivity: 430, GMV: 7200, GMVDay: 260, LM: 65, contribution: 17, GMVGrowth: 3.8, footfall: 920, salesCount: 185, inventoryTurnover: 2.1 },
  { store: '29002', group: 'Group A', department: 'Dept X', class: 'Class 2', subclass: 'Blankets', year: 2024, month: 11, linearMeter: 68, productivity: 410, GMV: 7100, GMVDay: 240, LM: 68, contribution: 16, GMVGrowth: 3.5, footfall: 910, salesCount: 175, inventoryTurnover: 2.0 },
  { store: '29002', group: 'Group A', department: 'Dept Z', class: 'Class 2', subclass: 'Mattress Protectors', year: 2024, month: 11, linearMeter: 62, productivity: 415, GMV: 7300, GMVDay: 255, LM: 62, contribution: 15, GMVGrowth: 3.2, footfall: 905, salesCount: 170, inventoryTurnover: 1.9 },
  { store: '29003', group: 'Group C', department: 'Dept Y', class: 'Class 3', subclass: 'Comforters', year: 2024, month: 11, linearMeter: 58, productivity: 460, GMV: 7400, GMVDay: 275, LM: 58, contribution: 21, GMVGrowth: 5.2, footfall: 1020, salesCount: 205, inventoryTurnover: 2.6 },
  { store: '29003', group: 'Group C', department: 'Dept Y', class: 'Class 3', subclass: 'Pillows', year: 2024, month: 11, linearMeter: 61, productivity: 470, GMV: 7600, GMVDay: 280, LM: 61, contribution: 22, GMVGrowth: 5.5, footfall: 1040, salesCount: 210, inventoryTurnover: 2.7 },
  { store: '29004', group: 'Group D', department: 'Dept W', class: 'Class 4', subclass: 'Table Covers', year: 2024, month: 12, linearMeter: 54, productivity: 390, GMV: 6900, GMVDay: 235, LM: 54, contribution: 14, GMVGrowth: 2.9, footfall: 870, salesCount: 160, inventoryTurnover: 1.8 },
  { store: '29004', group: 'Group D', department: 'Dept W', class: 'Class 4', subclass: 'Rugs', year: 2024, month: 12, linearMeter: 66, productivity: 395, GMV: 6950, GMVDay: 238, LM: 66, contribution: 13.5, GMVGrowth: 3.1, footfall: 880, salesCount: 162, inventoryTurnover: 1.85 },

  { store: '29005', group: 'Group A', department: 'Dept X', class: 'Class 5', subclass: 'Doormats', year: 2024, month: 12, linearMeter: 59, productivity: 405, GMV: 7100, GMVDay: 260, LM: 59, contribution: 15.2, GMVGrowth: 3.8, footfall: 890, salesCount: 170, inventoryTurnover: 2.0 },
  { store: '29005', group: 'Group A', department: 'Dept X', class: 'Class 5', subclass: 'Bath Towels', year: 2025, month: 1, linearMeter: 64, productivity: 415, GMV: 7200, GMVDay: 265, LM: 64, contribution: 16.5, GMVGrowth: 4.2, footfall: 895, salesCount: 175, inventoryTurnover: 2.1 },
  { store: '29006', group: 'Group B', department: 'Dept Z', class: 'Class 6', subclass: 'Hand Towels', year: 2025, month: 1, linearMeter: 68, productivity: 420, GMV: 7300, GMVDay: 270, LM: 68, contribution: 17, GMVGrowth: 4.5, footfall: 910, salesCount: 178, inventoryTurnover: 2.2 },
  { store: '29006', group: 'Group B', department: 'Dept Z', class: 'Class 6', subclass: 'Shower Curtains', year: 2025, month: 1, linearMeter: 72, productivity: 425, GMV: 7400, GMVDay: 275, LM: 72, contribution: 17.5, GMVGrowth: 4.8, footfall: 920, salesCount: 180, inventoryTurnover: 2.3 },
  { store: '29007', group: 'Group C', department: 'Dept W', class: 'Class 7', subclass: 'Wall Mirrors', year: 2025, month: 2, linearMeter: 63, productivity: 430, GMV: 7500, GMVDay: 280, LM: 63, contribution: 18, GMVGrowth: 5, footfall: 925, salesCount: 182, inventoryTurnover: 2.4 },
  { store: '29007', group: 'Group C', department: 'Dept W', class: 'Class 7', subclass: 'Decor Vases', year: 2025, month: 2, linearMeter: 67, productivity: 435, GMV: 7600, GMVDay: 285, LM: 67, contribution: 18.5, GMVGrowth: 5.2, footfall: 930, salesCount: 185, inventoryTurnover: 2.5 },
  { store: '29008', group: 'Group D', department: 'Dept Y', class: 'Class 8', subclass: 'Wall Art', year: 2025, month: 2, linearMeter: 69, productivity: 440, GMV: 7700, GMVDay: 290, LM: 69, contribution: 19, GMVGrowth: 5.3, footfall: 940, salesCount: 188, inventoryTurnover: 2.55 },
  { store: '29008', group: 'Group D', department: 'Dept Y', class: 'Class 8', subclass: 'Photo Frames', year: 2025, month: 2, linearMeter: 70, productivity: 445, GMV: 7800, GMVDay: 295, LM: 70, contribution: 19.5, GMVGrowth: 5.4, footfall: 950, salesCount: 190, inventoryTurnover: 2.6 },
  { store: '29009', group: 'Group E', department: 'Dept V', class: 'Class 9', subclass: 'Clocks', year: 2025, month: 3, linearMeter: 73, productivity: 450, GMV: 7900, GMVDay: 300, LM: 73, contribution: 20, GMVGrowth: 5.5, footfall: 960, salesCount: 195, inventoryTurnover: 2.7 },
  { store: '29009', group: 'Group E', department: 'Dept V', class: 'Class 9', subclass: 'Lamps', year: 2025, month: 3, linearMeter: 74, productivity: 455, GMV: 8000, GMVDay: 305, LM: 74, contribution: 20.5, GMVGrowth: 5.6, footfall: 970, salesCount: 198, inventoryTurnover: 2.8 },

  // 10 more for padding
  { store: '29010', group: 'Group F', department: 'Dept U', class: 'Class 10', subclass: 'Lanterns', year: 2025, month: 3, linearMeter: 75, productivity: 460, GMV: 8100, GMVDay: 310, LM: 75, contribution: 21, GMVGrowth: 5.7, footfall: 980, salesCount: 200, inventoryTurnover: 2.9 },
  { store: '29010', group: 'Group F', department: 'Dept U', class: 'Class 10', subclass: 'Candles', year: 2025, month: 3, linearMeter: 76, productivity: 465, GMV: 8200, GMVDay: 315, LM: 76, contribution: 21.5, GMVGrowth: 5.8, footfall: 985, salesCount: 202, inventoryTurnover: 3.0 },
  { store: '29011', group: 'Group G', department: 'Dept T', class: 'Class 11', subclass: 'Planters', year: 2025, month: 4, linearMeter: 77, productivity: 470, GMV: 8300, GMVDay: 320, LM: 77, contribution: 22, GMVGrowth: 6, footfall: 990, salesCount: 205, inventoryTurnover: 3.1 },
  { store: '29011', group: 'Group G', department: 'Dept T', class: 'Class 11', subclass: 'Vases', year: 2025, month: 4, linearMeter: 78, productivity: 475, GMV: 8400, GMVDay: 325, LM: 78, contribution: 22.5, GMVGrowth: 6.1, footfall: 995, salesCount: 208, inventoryTurnover: 3.2 },
  { store: '29012', group: 'Group H', department: 'Dept S', class: 'Class 12', subclass: 'Kitchen Towels', year: 2025, month: 4, linearMeter: 79, productivity: 480, GMV: 8500, GMVDay: 330, LM: 79, contribution: 23, GMVGrowth: 6.2, footfall: 1000, salesCount: 210, inventoryTurnover: 3.3 },
  { store: '29012', group: 'Group H', department: 'Dept S', class: 'Class 12', subclass: 'Table Napkins', year: 2025, month: 4, linearMeter: 80, productivity: 485, GMV: 8600, GMVDay: 335, LM: 80, contribution: 23.5, GMVGrowth: 6.3, footfall: 1010, salesCount: 215, inventoryTurnover: 3.4 },
  { store: '29013', group: 'Group I', department: 'Dept R', class: 'Class 13', subclass: 'Baskets', year: 2025, month: 5, linearMeter: 81, productivity: 490, GMV: 8700, GMVDay: 340, LM: 81, contribution: 24, GMVGrowth: 6.4, footfall: 1020, salesCount: 218, inventoryTurnover: 3.5 },
  { store: '29013', group: 'Group I', department: 'Dept R', class: 'Class 13', subclass: 'Storage Boxes', year: 2025, month: 5, linearMeter: 82, productivity: 495, GMV: 8800, GMVDay: 345, LM: 82, contribution: 24.5, GMVGrowth: 6.5, footfall: 1030, salesCount: 220, inventoryTurnover: 3.6 },
  { store: '29014', group: 'Group J', department: 'Dept Q', class: 'Class 14', subclass: 'Trays', year: 2025, month: 5, linearMeter: 83, productivity: 500, GMV: 8900, GMVDay: 350, LM: 83, contribution: 25, GMVGrowth: 6.6, footfall: 1040, salesCount: 222, inventoryTurnover: 3.7 },
  { store: '29014', group: 'Group J', department: 'Dept Q', class: 'Class 14', subclass: 'Coasters', year: 2025, month: 5, linearMeter: 84, productivity: 505, GMV: 9000, GMVDay: 355, LM: 84, contribution: 25.5, GMVGrowth: 6.7, footfall: 1050, salesCount: 225, inventoryTurnover: 3.8 }
]

// Dynamic filter options
const dynamicGroupOptions = computed(() => {
  let data = rawData
  if (selectedStore.value) data = data.filter(row => row.store == selectedStore.value.value)
  return [...new Set(data.map(row => row.group))].map(group => ({ label: group, value: group }))
})
const dynamicDepartmentOptions = computed(() => {
  let data = rawData
  if (selectedStore.value) data = data.filter(row => row.store == selectedStore.value.value)
  if (selectedGroups.value.length) data = data.filter(row => selectedGroups.value.some(g => g.value === row.group))
  return [...new Set(data.map(row => row.department))].map(dept => ({ label: dept, value: dept }))
})
const dynamicClassOptions = computed(() => {
  let data = rawData
  if (selectedStore.value) data = data.filter(row => row.store == selectedStore.value.value)
  if (selectedGroups.value.length) data = data.filter(row => selectedGroups.value.some(g => g.value === row.group))
  if (selectedDepartments.value.length) data = data.filter(row => selectedDepartments.value.some(d => d.value === row.department))
  return [...new Set(data.map(row => row.class))].map(cls => ({ label: cls, value: cls }))
})
const dynamicSubclassOptions = computed(() => {
  let data = rawData
  if (selectedStore.value) data = data.filter(row => row.store == selectedStore.value.value)
  if (selectedGroups.value.length) data = data.filter(row => selectedGroups.value.some(g => g.value === row.group))
  if (selectedDepartments.value.length) data = data.filter(row => selectedDepartments.value.some(d => d.value === row.department))
  if (selectedClasses.value.length) data = data.filter(row => selectedClasses.value.some(c => c.value === row.class))
  return [...new Set(data.map(row => row.subclass))].map(subclass => ({ label: subclass, value: subclass }))
})

const hasData = computed(() => filtered.value.length > 0)

// Chart refs
let lineChart, top5Chart, bottom5Chart

function applyFilters() {
  filtered.value = rawData.filter(row =>
    (selectedStore.value ? row.store == selectedStore.value.value : true) &&
    (selectedGroups.value.length === 0 || selectedGroups.value.some(g => g.value === row.group)) &&
    (selectedDepartments.value.length === 0 || selectedDepartments.value.some(d => d.value === row.department)) &&
    (selectedClasses.value.length === 0 || selectedClasses.value.some(c => c.value === row.class)) &&
    (selectedSubclasses.value.length === 0 || selectedSubclasses.value.some(s => s.value === row.subclass)) &&
    (
      (row.year > fromYear.value || (row.year === fromYear.value && row.month >= fromMonth.value)) &&
      (row.year < toYear.value || (row.year === toYear.value && row.month <= toMonth.value))
    )
  )

  // Aggregate by month for main chart
  const monthLabels = []
  const linearMeterData = []
  const productivityData = []

  let y = fromYear.value, m = fromMonth.value
  while (y < toYear.value || (y === toYear.value && m <= toMonth.value)) {
    const monthRows = filtered.value.filter(r => r.year === y && r.month === m)
    if (monthRows.length) {
      monthLabels.push(`${String(m).padStart(2, '0')}-${y}`)
      linearMeterData.push(monthRows.reduce((sum, r) => sum + r.linearMeter, 0))
      productivityData.push(monthRows.reduce((sum, r) => sum + r.productivity, 0))
    }
    m++
    if (m > 12) {
      m = 1
      y++
    }
  }

  // Destroy previous chart instances
  if (lineChart) lineChart.destroy()
  const lineCtx = document.getElementById('lineChart')
  if (lineCtx) {
    lineChart = new Chart(lineCtx, {
      type: 'line',
      data: {
        labels: monthLabels,
        datasets: [
          { label: 'Linear Meter', data: linearMeterData, borderColor: '#007bff', yAxisID: 'y1', tension: 0.4 },
          { label: 'Productivity', data: productivityData, borderColor: 'orange', yAxisID: 'y2', tension: 0.4 }
        ]
      },
      options: {
        scales: {
          y1: { type: 'linear', position: 'left', title: { display: true, text: 'Linear Meter' } },
          y2: { type: 'linear', position: 'right', title: { display: true, text: 'Productivity' }, grid: { drawOnChartArea: false } },
          x: { title: { display: true, text: 'Month' } }
        },
        plugins: {
          title: { display: true, text: 'Linear Meter vs Productivity' }
        }
      }
    })
  }

  nextTick(() => {
    updateBarCharts(filtered.value)
  })
}

function updateBarCharts(filteredData) {
  // Top 5 and Bottom 5 by selected metric
  const metric = selectedMetric.value.value
  const grouped = {}
  filteredData.forEach(row => {
    const key = row.subclass
    if (!grouped[key]) grouped[key] = 0
    grouped[key] += row[metric] || 0
  })
  const sorted = Object.entries(grouped).sort((a, b) => b[1] - a[1])
  const top5 = sorted.slice(0, 5)
  const bottom5 = sorted.slice(-5).reverse()

  // Destroy previous chart instances
  if (top5Chart) top5Chart.destroy()
  if (bottom5Chart) bottom5Chart.destroy()

  const topCtx = document.getElementById('top5Chart')
  const bottomCtx = document.getElementById('bottom5Chart')

  if (topCtx) {
    top5Chart = new Chart(topCtx, {
      type: 'bar',
      data: {
        labels: top5.map(([k]) => k),
        datasets: [{ label: selectedMetric.value.label, data: top5.map(([, v]) => v), backgroundColor: 'green' }]
      },
      options: {
        plugins: { title: { display: true, text: `Top 5 Subclasses by ${selectedMetric.value.label}` } },
        scales: { x: { title: { display: true, text: 'Subclass' } }, y: { title: { display: true, text: selectedMetric.value.label } } }
      }
    })
  }

  if (bottomCtx) {
    bottom5Chart = new Chart(bottomCtx, {
      type: 'bar',
      data: {
        labels: bottom5.map(([k]) => k),
        datasets: [{ label: selectedMetric.value.label, data: bottom5.map(([, v]) => v), backgroundColor: 'red' }]
      },
      options: {
        plugins: { title: { display: true, text: `Bottom 5 Subclasses by ${selectedMetric.value.label}` } },
        scales: { x: { title: { display: true, text: 'Subclass' } }, y: { title: { display: true, text: selectedMetric.value.label } } }
      }
    })
  }
}

// Re-render bar charts when metric changes
watch(selectedMetric, () => {
  // Use last filtered data or all data if not filtered yet
  applyFilters()
})

// Initial load: select first store and show all data for it
onMounted(() => {
  applyFilters()
})
</script>

<style scoped>
@import "vue-multiselect/dist/vue-multiselect.css";

canvas {
  max-width: 100%;
}
</style>
