# Build stage
FROM node:20-alpine as build

WORKDIR /app
COPY package*.json ./
RUN npm ci

COPY . .
ENV VITE_BASE_URL="https://lmdllcloudapp-dev.landmarkgroup.com/spaceoptimization/api"
ENV VITE_AD_REDIRECT_URI="https://lmdllcloudapp-dev.landmarkgroup.com/spaceoptimization/callback"
ENV VITE_BASE_PATH="/spaceoptimization"

RUN npm run build

# Production stage
FROM nginx:alpine

COPY --from=build /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/conf.d/default.conf

# 🔧 Fix file permissions
RUN chown -R nginx:nginx /usr/share/nginx/html && \
    chmod -R 755 /usr/share/nginx/html

# Optional: drop root privileges (safest)
# USER nginx

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
