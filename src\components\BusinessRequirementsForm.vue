<template>
  <div class="space-y-6 px-10 rounded-3xl border">
    <div class="grid grid-cols-1 md:grid-cols-2">
      <div class="space-y-3 w-2/4">
        <label class="flex block text-sm font-medium mb-2">
          <Space class="w-4 h-4 mr-2 text-secondary" />Scenario Name<span class="text-red-500 ml-1">*</span>
        </label>
        <input type="input" v-model="formData.evaluationPeriod.startDate" placeholder="Enter Scenario Name"
          class="w-full bg-white p-2 h-10 border border-primary text-xs rounded focus:outline-none focus:ring-2 focus:ring-primary" />
      </div>
      <!-- Season Preference Toggle -->
      <div class="space-y-3 w-2/4">
        <label class="flex items-center text-sm font-semibold">
          <Bolt class="w-4 h-4 mr-2 text-secondary" />
          Store Configuration <span class="text-red-500 ml-1">*</span>
        </label>
        <Multiselect v-model="formData.configurationStore" :options="configurationStoreOptions" open-direction="bottom"
          placeholder="Select Store Configuration" class="multiselect-custom" />
      </div>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2">
      <div class="space-y-3 w-2/4">
        <label class="flex items-center text-sm font-semibold">
          <SunSnow class="w-4 h-4 mr-2 text-secondary" />
          Season Type <span class="text-red-500 ml-1">*</span>
        </label>
        <Multiselect v-model="formData.seasonType" :options="seasonTypeOptions" open-direction="bottom"
          placeholder="Select Season Type" track-by="value" label="label" class="multiselect-custom" />
      </div>
      <!-- Form Inputs -->
      <!-- Performance Metric -->
      <div class="space-y-3 w-2/4">
        <label class="flex items-center text-sm font-semibold">
          <Target class="w-4 h-4 mr-2 text-secondary" />
          Performance Metric <span class="text-red-500 ml-1">*</span>
        </label>
        <Multiselect v-model="formData.performanceMetric" :options="performanceOptions" open-direction="bottom"
          placeholder="Select performance metric" track-by="value" label="label" class="multiselect-custom" />
      </div>
    </div>

    <!-- Store Selection -->
    <div class="grid grid-cols-1 md:grid-cols-2">
      <div v-if="formData.configurationStore === 'Selected Stores'" class="space-y-3 w-2/4">
        <label class="flex items-center text-sm font-semibold">
          <Filter class="w-4 h-4 mr-2 text-secondary" />
          Store Selection <span class="text-red-500 ml-1">*</span>
        </label>
        <Multiselect v-model="formData.storeSelection" :options="storeOptions" :multiple="true" :close-on-select="false"
          open-direction="bottom" placeholder="Select stores" label="label" track-by="value"
          class="multiselect-custom" />
      </div>
      <div class="space-y-3 w-2/4">
        <label class="flex items-center text-sm font-semibold">
          <SunSnow class="w-4 h-4 mr-2 text-secondary" />
          Event <span class="text-red-500 ml-1">*</span>
        </label>
        <input type="input" v-model="formData.event" placeholder="Enter Event Name"
          class="w-full bg-white p-2 h-10 border border-primary rounded text-xs focus:outline-none focus:ring-2 focus:ring-primary" />
      </div>
    </div>

    <!-- Recommendation Period -->
    <div class=" text-sm">
      <h3 class="text-md font-semibold mb-2 flex items-center">
        <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center mr-3">
          <Calendar class="w-4 h-4 text-secondary" />
        </div>
        Evaluation Period <span class="text-red-500 ml-1">*</span>
      </h3>
      <div class="grid grid-cols-1 md:grid-cols-2">
        <div class="w-2/4">
          <label class="block text-sm font-medium mb-2">Start Date</label>
          <input type="date" v-model="formData.evaluationPeriod.startDate" placeholder="Select Start Date"
            class="w-full px-4 py-3 bg-white h-10 border border-primary rounded focus:ring-2 focus:ring-secondary" />
        </div>
        <div class="w-2/4">
          <label class="block text-sm font-medium mb-2">End Date</label>
          <input type="date" v-model="formData.evaluationPeriod.endDate" placeholder="Select End Date"
            class="w-full px-4 py-3 bg-white border h-10 border-primary rounded focus:ring-2 focus:ring-secondary" />
        </div>
      </div>
    </div>
    <div class=" text-sm">
      <h3 class="text-md font-semibold mb-2 flex items-center">
        <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center mr-3">
          <Calendar class="w-4 h-4 text-secondary" />
        </div>
        Reference Period <span class="text-red-500 ml-1">*</span>
      </h3>
      <div class="grid grid-cols-1 md:grid-cols-2">
        <div class="w-2/4">
          <label class="block text-sm font-medium mb-2">Start Date</label>
          <input type="date" v-model="formData.referencePeriod.startDate"
            class="w-full px-4 py-3 bg-white h-10 text-sm border border-green-100 rounded focus:ring-2 focus:ring-secondary" />
        </div>
        <div class="w-2/4">
          <label class="block text-sm font-medium mb-2">End Date</label>
          <input type="date" v-model="formData.referencePeriod.endDate"
            class="w-full px-4 py-3 bg-white border text-sm h-10 border-green-100 rounded focus:ring-2 focus:ring-secondary" />
        </div>
      </div>
    </div>
  </div>

  <!-- File Upload Section -->
  <div class="p-10">
    <div class="flex items-center mb-4">
      <div class="w-8 h-8 bg-primary rounded-xl flex items-center justify-center mr-4">
        <Upload class="w-4 h-4 text-secondary" />
      </div>
      <div>
        <h2 class="text-md font-bold">Upload Files</h2>
      </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <FileUploadArea fileKey="sqftFile" label="SQFT file" :icon="BarChart3" />
      <FileUploadArea fileKey="mdqRos" label="MDQ/ROS" :icon="TrendingUp" />
      <FileUploadArea fileKey="subclassCover" label="Subclass Cover" :icon="FileText" />
      <FileUploadArea fileKey="exclusions" label="Exclusions" :icon="SquaresExclude" />
    </div>
  </div>
  <!-- <div class="flex justify-end px-10 m-1">
    <button type="button" @click="generateAnalysis"
      class="bg-gradient-to-r from-secondary/80 to-tertiary text-white rounded font-semibold shadow shadow-green-500/25 hover:from-tertiary hover:to-secondary/80 transition text-sm px-6 py-3 duration-300">
      Continue
    </button>
  </div> -->
</template>


<script setup>
import { ref, reactive, provide } from 'vue'
import Multiselect from 'vue-multiselect'
import Datepicker from 'vue3-datepicker'
import FileUploadArea from './FileUploadArea.vue'
import { Calendar, Filter, ChevronDown, Space, Target, Check, X, Upload, BarChart3, TrendingUp, FileText, SquaresExclude, Bolt, SunSnow } from 'lucide-vue-next'

const uploadedFiles = ref({
  sqftFile: null,
  mdqRos: null,
  subclassCover: null,
  exclusions: null
})

const dragActive = ref(null)

const handleFileUpload = (key, file) => {
  uploadedFiles.value[key] = file
}

const setDragActive = (key) => {
  dragActive.value = key
}

provide('uploadedFiles', uploadedFiles)
provide('dragActive', dragActive)
provide('handleFileUpload', handleFileUpload)
provide('setDragActive', setDragActive)

const formData = reactive({
  configurationStore: null,
  seasonPreference: 'in-season',
  performanceMetric: null,
  storeSelection: [],
  event: null,
  evaluationPeriod: {
    startDate: '',
    endDate: ''
  },
  referencePeriod: {
    startDate: '',
    endDate: ''
  }
})

const storeDropdownOpen = ref(false)
const storeOptions = [
  { value: 'Store 001 - Downtown', label: 'Store 001 - Downtown' },
  { value: 'Store 002 - Mall Plaza', label: 'Store 002 - Mall Plaza' },
  { value: 'Store 003 - Westside', label: 'Store 003 - Westside' },
  { value: 'Store 004 - Northgate', label: 'Store 004 - Northgate' },
  { value: 'Store 005 - Eastpark', label: 'Store 005 - Eastpark' }
]
const configurationStoreOptions = ['Test & Control', 'Selected Stores']
const seasonTypeOptions = [
  { value: 'in season', label: 'In Season' },
  { value: 'pre season', label: 'Pre Season' }
]
const performanceOptions = [
  { value: 'revenue', label: 'Revenue' },
  { value: 'margin', label: 'Margin' }
]
const eventOptions = [
  { value: 'Eid', label: 'Eid' },
  { value: 'Ramadan', label: 'Ramadan' },
  { value: 'Back to School', label: 'Back to School' },
  { value: 'Summer Sale', label: 'Summer Sale' }
]
const toggleStoreSelection = (store) => {
  const index = formData.storeSelection.indexOf(store)
  if (index === -1) {
    formData.storeSelection.push(store)
  } else {
    formData.storeSelection.splice(index, 1)
  }
}
const generateAnalysis = () => {
  console.log('Generating analysis with:', JSON.stringify(formData, null, 2))
  // You can replace this with actual logic
}

const removeStore = (store) => {
  formData.storeSelection = formData.storeSelection.filter(s => s !== store)
}
</script>

<style scoped>
.rotate-180 {
  transform: rotate(180deg);
}
</style>
