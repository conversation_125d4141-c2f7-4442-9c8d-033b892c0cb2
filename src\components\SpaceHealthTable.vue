<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { spaceHealthData, SpaceHealthRow } from '../data/spaceHealthData';

const expandedGroups = ref<string[]>([]);
const expandedDepartments = ref<Record<string, string[]>>({});
const expandedClasses = ref<Record<string, string[]>>({});

const groupRows = computed(() => spaceHealthData.slice(0, 3)); // Show 3 groups
console.log("grouprows", groupRows.value)
const isGroupExpanded = (group: string) => expandedGroups.value.includes(group);
const isDepartmentExpanded = (group: string, department: string) => expandedDepartments.value[group]?.includes(department);
const isClassExpanded = (group: string, department: string, className: string) => expandedClasses.value[`${group}|${department}`]?.includes(className);

const toggleGroup = (group: string) => {
  const idx = expandedGroups.value.indexOf(group);
  if (idx > -1) expandedGroups.value.splice(idx, 1);
  else expandedGroups.value.push(group);
};

const toggleDepartment = (group: string, department: string) => {
  if (!expandedDepartments.value[group]) expandedDepartments.value[group] = [];
  const idx = expandedDepartments.value[group].indexOf(department);
  if (idx > -1) expandedDepartments.value[group].splice(idx, 1);
  else expandedDepartments.value[group].push(department);
};

const toggleClass = (group: string, department: string, className: string) => {
  const depKey = `${group}|${department}`;
  if (!expandedClasses.value[depKey]) expandedClasses.value[depKey] = [];
  const idx = expandedClasses.value[depKey].indexOf(className);
  if (idx > -1) expandedClasses.value[depKey].splice(idx, 1);
  else expandedClasses.value[depKey].push(className);
};

onMounted(() => {
  expandedGroups.value = [];
  expandedDepartments.value = {};
  expandedClasses.value = {};
});

function chevron(down: boolean) {
  return down
    ? `<svg xmlns='http://www.w3.org/2000/svg' class='h-4 w-4' fill='none' viewBox='0 0 24 24' stroke='currentColor'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'/></svg>`
    : `<svg xmlns='http://www.w3.org/2000/svg' class='h-4 w-4' fill='none' viewBox='0 0 24 24' stroke='currentColor'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M9 5l7 7-7 7'/></svg>`;
}

const allColumns = [
  'Store ID', 'Group', 'Department', 'Class', 'Sub Class', 'Sqft Exists', 'LM', 'Sqft', 'LM Rank', 'Sqft Rank', 'Option Count', 'Option per SQFT', 'SOH Qty', 'SOH per SQFT', 'Revenue(AED)', 'GMV', 'Rev per SQFT(AED)', 'GMV per LM', 'Cost', 'Avg Stock', 'Present ROS', 'Cover (days)', 'LM Cont', 'SQFT Cont', 'Diff'
];

const perUnitFields = [
  'lmPerSqft', 'optionPerLm', 'optionPerSqft', 'sohPerLm', 'sohPerSqft', 'revenuePerDay', 'gmvPerDay', 'revPerLm', 'revPerSqft', 'gmvPerLm', 'gmvPerSqft', 'revPerLmPerDay', 'revPerSqftPerDay', 'gmvPerLmPerDay', 'gmvPerSqftPerDay', 'presentRos'
];

function aggregateTotals(rows: SpaceHealthRow[]): Partial<SpaceHealthRow> {
  // Sum for sum fields, average for per-unit fields
  const total: any = {};
  for (const col of [
    'lm', 'sqft', 'lm2', 'sqft2', 'optionCount', 'sohQty', 'revenue', 'gmv', 'cost', 'avgStock', 'coverDays', 'lmCont', 'sqftCont', 'diff']) {
    total[col] = rows.reduce((sum, r) => sum + (r[col] || 0), 0);
  }
  for (const col of ['optionPerSqft', 'sohPerSqft', 'revPerSqft', 'gmvPerLm', 'presentRos']) {
    total[col] = +(rows.reduce((sum, r) => sum + (r[col] || 0), 0) / rows.length).toFixed(2);
  }
  console.log('aggregateTotals', total);
  // For non-numeric fields, just show label or blank
  total.storeId = rows[0]?.storeId || '';
  total.group = rows[0]?.group || '';
  total.department = rows[0]?.department || '';
  total.class = rows[0]?.class || '';
  total.subClass = rows[0]?.subClass || '';
  total.sqftExists = 'Yes';
  return total;
}

// Helper to map column display name to field name
function colMap(col: string): string {
  // Explicit mapping for rank columns with spaces
  if (col === 'LM Rank') return 'lm2';
  if (col === 'Sqft Rank') return 'sqft2';
  if (col === 'GMV per LM') return 'gmvPerLm';
  console.log('colMap', col);
  return col
    .replace(/ /g, '')
    .replace('(SPD)', 'PerDay')
    .replace('StoreID', 'storeId')
    .replace('Group', 'group')
    .replace('Department', 'department')
    .replace('Class', 'class')
    .replace('SubClass', 'subClass')
    .replace('SqftExists', 'sqftExists')
    .replace('LM', 'lm')
    .replace('Sqft', 'sqft')
    .replace('LMRank', 'lm2')
    .replace('SqftRank', 'sqft2')
    .replace('LMperSQFT', 'lmPerSqft')
    .replace('OptionCount', 'optionCount')
    .replace('OptionperLM', 'optionPerLm')
    .replace('OptionperSQFT', 'optionPerSqft')
    .replace('SOHQty', 'sohQty')
    .replace('SOHperLM', 'sohPerLm')
    .replace('SOHperSQFT', 'sohPerSqft')
    .replace('Revenue\(AED\)', 'revenue')
    .replace('GMV', 'gmv')
    .replace('RevenueperdayPerDay', 'revenuePerDay')
    .replace('GMVperday', 'gmvPerDay')
    .replace('RevperLM', 'revPerLm')
    .replace('RevperSQFT\(AED\)', 'revPerSqft')
    .replace('GMVperLM', 'gmvPerLm')
    .replace('GMVperSQFT', 'gmvPerSqft')
    .replace('RevperLMperday', 'revPerLmPerDay')
    .replace('RevperSQFTperday', 'revPerSqftPerDay')
    .replace('GMVperLMperday', 'gmvPerLmPerDay')
    .replace('GMVperSQFTperday', 'gmvPerSqftPerDay')
    .replace('Cost', 'cost')
    .replace('AvgStock', 'avgStock')
    .replace('PresentROS', 'presentRos')
    .replace('Cover(days)', 'coverDays')
    .replace('LMCont', 'lmCont')
    .replace('SQFTCont', 'sqftCont')
    .replace('Diff', 'diff');
}

// Add a helper to format numbers with 1 decimal and comma separators
function formatNumber(val: number): string {
  if (typeof val !== 'number' || isNaN(val)) return '';
  // Show 1 decimal only if needed
  return val % 1 === 0
    ? val.toLocaleString('en-US', { maximumFractionDigits: 0 })
    : val.toLocaleString('en-US', { minimumFractionDigits: 1, maximumFractionDigits: 1 });
}

// Add a helper to display a placeholder for empty strings
function displayValue(val: any): string {
  if (typeof val === 'number') return formatNumber(val);
  if (val === '' || val === undefined || val === null) return '-';
  console.log('displayValue', val);
  return val;
}

// Define column widths
const columnWidths = [70, 140, 204, 174, 174]; // Store ID, Group, Department, Class, Sub Class
const stickyLeftPositions = [
  0, // Store ID
  60, // Group
  180, // Department (120 + 144)
  260, // Class (120 + 144 + 184)
  300  // Sub Class (120 + 144 + 184 + 144)
];
</script>

<template>
  <div class="">
    <!-- Table container with horizontal scroll -->
    <div class=" max-w-full border border-gray-200 rounded-lg shadow-sm ">
      <table class=" divide-y divide-gray-200">
        <thead class="bg-primary sticky top-0 z-50">
          <tr>
            <th v-for="(header, idx) in allColumns" :key="header" :class="[
              'py-3 px-4 text-center text-xs font-bold bg-primary uppercase tracking-wider whitespace-nowrap border-r border-gray-300 last:border-r-0',
              idx < 5 ? 'sticky bg-primary z-40' : 'min-w-[1px]',
            ]" :style="idx < 5 ? { 
                left: stickyLeftPositions[idx] + 'px',
                minWidth: columnWidths[idx] + 'px'
              } : { minWidth: '1px' }">
              {{ header }}
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <template v-for="(groupRow, gIdx) in groupRows" :key="groupRow.group">
            <!-- Group Total Row -->
            <tr class="bg-white font-bold cursor-pointer hover:bg-gray-50 transition-colors"
              @click="toggleGroup(groupRow.group)">
              <td v-for="(col, idx) in allColumns" :key="col" :class="[
                'py-3 px-4 text-center whitespace-nowrap border-r border-gray-200 last:border-r-0',
                idx < 5 ? 'sticky bg-white z-30 shadow-sm' : '',
              ]" :style="idx < 5 ? { 
                left: stickyLeftPositions[idx] + 'px',
                minWidth: columnWidths[idx] + 'px'
              } : { minWidth: '120px' }">
                <div v-if="idx === 0" class="truncate max-w-[120px]" :title="displayValue(groupRow.storeId)">
                  {{ displayValue(groupRow.storeId) }}
                </div>
                <div v-else-if="idx === 1" class="flex items-center justify-center gap-2">
                  <span v-html="chevron(isGroupExpanded(groupRow.group))" 
                    aria-label="Toggle group"
                    :aria-expanded="isGroupExpanded(groupRow.group)" 
                    class="flex-shrink-0 w-4 h-4"></span>
                  <span class="truncate max-w-[100px]" :title="displayValue(groupRow.group) + ' Total'">
                    {{ displayValue(groupRow.group) }} Total
                  </span>
                </div>
                <div v-else-if="idx === 2" class="truncate max-w-[120px]" :title="displayValue(groupRow.department)">
                  {{ displayValue(groupRow.department) }}
                </div>
                <div v-else-if="idx === 3" class="truncate max-w-[120px]" :title="displayValue(groupRow.class)">
                  {{ displayValue(groupRow.class) }}
                </div>
                <div v-else-if="idx === 4" class="truncate max-w-[120px]" :title="displayValue(groupRow.subClass)">
                  {{ displayValue(groupRow.subClass) }}
                </div>
                <div v-else class="truncate max-w-[120px]" :title="displayValue((aggregateTotals([groupRow]) as any)[colMap(col)])">
                  {{ displayValue((aggregateTotals([groupRow]) as any)[colMap(col)]) }}
                </div>
              </td>
            </tr>

            <template v-if="isGroupExpanded(groupRow.group)">
              <template v-for="(deptRow, dIdx) in groupRow.children || []" :key="deptRow.department">
                <!-- Department Total Row -->
                <tr class="bg-gray-25 font-semibold cursor-pointer hover:bg-gray-75 transition-colors"
                  @click.stop="toggleDepartment(groupRow.group, deptRow.department)">
                  <td v-for="(col, idx) in allColumns" :key="col" :class="[
                    'py-2 px-4 text-center whitespace-nowrap border-r border-gray-200 last:border-r-0',
                    idx < 5 ? 'sticky bg-gray-25 z-30 shadow-sm' : ''
                  ]" :style="idx < 5 ? { 
                    left: stickyLeftPositions[idx] + 'px',
                    minWidth: columnWidths[idx] + 'px'
                  } : { minWidth: '1px' }">
                    <div v-if="idx === 0" class="truncate max-w-[120px] pl-4" :title="displayValue(deptRow.storeId)">
                      {{ displayValue(deptRow.storeId) }}
                    </div>
                    <div v-else-if="idx === 1" class="truncate max-w-[120px] pl-4" :title="displayValue(deptRow.group)">
                      {{ displayValue(deptRow.group) }}
                    </div>
                    <div v-else-if="idx === 2" class="flex items-center justify-center gap-2">
                      <span v-html="chevron(isDepartmentExpanded(groupRow.group, deptRow.department))"
                        aria-label="Toggle department"
                        :aria-expanded="isDepartmentExpanded(groupRow.group, deptRow.department)"
                        class="flex-shrink-0 w-4 h-4"></span>
                      <span class="truncate max-w-[100px]" :title="displayValue(deptRow.department) + ' Total'">
                        {{ displayValue(deptRow.department) }} Total
                      </span>
                    </div>
                    <div v-else-if="idx < 5" class="truncate max-w-[120px]">-</div>
                    <div v-else class="truncate max-w-[120px]" :title="displayValue((aggregateTotals([deptRow]) as any)[colMap(col)])">
                      {{ displayValue((aggregateTotals([deptRow]) as any)[colMap(col)]) }}
                    </div>
                  </td>
                </tr>

                <template v-if="isDepartmentExpanded(groupRow.group, deptRow.department)">
                  <template v-for="(classRow, cIdx) in deptRow.children || []" :key="classRow.class">
                    <!-- Class Total Row -->
                    <tr class="bg-gray-50 font-normal cursor-pointer hover:bg-gray-100 transition-colors"
                      @click.stop="toggleClass(groupRow.group, deptRow.department, classRow.class)">
                      <td v-for="(col, idx) in allColumns" :key="col" :class="[
                        'py-2 px-4 text-center whitespace-nowrap border-r border-gray-200 last:border-r-0',
                        idx < 5 ? 'sticky bg-gray-50 z-30 shadow-sm' : '',
                      ]" :style="idx < 5 ? { 
                        left: stickyLeftPositions[idx] + 'px',
                        minWidth: columnWidths[idx] + 'px'
                      } : { minWidth: '120px' }">
                        <div v-if="idx === 0" class="truncate max-w-[120px] pl-8" :title="displayValue(classRow.storeId)">
                          {{ displayValue(classRow.storeId) }}
                        </div>
                        <div v-else-if="idx === 1" class="truncate max-w-[120px] pl-8" :title="displayValue(classRow.group)">
                          {{ displayValue(classRow.group) }}
                        </div>
                        <div v-else-if="idx === 2" class="truncate max-w-[120px] pl-8" :title="displayValue(classRow.department)">
                          {{ displayValue(classRow.department) }}
                        </div>
                        <div v-else-if="idx === 3" class="flex items-center justify-center gap-2">
                          <span v-html="chevron(isClassExpanded(groupRow.group, deptRow.department, classRow.class))"
                            aria-label="Toggle class"
                            :aria-expanded="isClassExpanded(groupRow.group, deptRow.department, classRow.class)"
                            class="flex-shrink-0 w-4 h-4"></span>
                          <span class="truncate max-w-[100px]" :title="displayValue(classRow.class) + ' Total'">
                            {{ displayValue(classRow.class) }} Total
                          </span>
                        </div>
                        <div v-else-if="idx < 5" class="truncate max-w-[120px]">-</div>
                        <div v-else class="truncate max-w-[120px]" :title="displayValue((aggregateTotals([classRow]) as any)[colMap(col)])">
                          {{ displayValue((aggregateTotals([classRow]) as any)[colMap(col)]) }}
                        </div>
                      </td>
                    </tr>

                    <template v-if="isClassExpanded(groupRow.group, deptRow.department, classRow.class)">
                      <template v-for="(subRow, sIdx) in classRow.children || []" :key="subRow.subClass">
                        <!-- Subclass Row -->
                        <tr :class="[
                          'transition-colors border-b border-gray-100',
                          sIdx % 2 === 0 ? 'bg-white hover:bg-blue-25' : 'bg-gray-25 hover:bg-blue-50'
                        ]">
                          <td v-for="(col, idx) in allColumns" :key="col" :class="[
                            'py-2 px-4 text-center whitespace-nowrap border-r border-gray-200 last:border-r-0',
                            idx < 5 ? 'sticky z-30 shadow-sm' : '',
                            idx < 5 && sIdx % 2 === 0 ? 'bg-white' : idx < 5 ? 'bg-gray-25' : ''
                          ]" :style="idx < 5 ? { 
                            left: stickyLeftPositions[idx] + 'px',
                            minWidth: columnWidths[idx] + 'px'
                          } : { minWidth: '120px' }">
                            <div v-if="idx === 0" class="truncate max-w-[120px] pl-12" :title="(subRow as any).storeId">
                              {{ (subRow as any).storeId }}
                            </div>
                            <div v-else-if="idx === 1" class="truncate max-w-[120px] pl-12" :title="(subRow as any).group">
                              {{ (subRow as any).group }}
                            </div>
                            <div v-else-if="idx === 2" class="truncate max-w-[120px] pl-12" :title="(subRow as any).department">
                              {{ (subRow as any).department }}
                            </div>
                            <div v-else-if="idx === 3" class="truncate max-w-[120px] pl-12" :title="(subRow as any).class">
                              {{ (subRow as any).class }}
                            </div>
                            <div v-else-if="idx === 4" class="truncate max-w-[120px] pl-12" :title="(subRow as any).subClass">
                              {{ (subRow as any).subClass }}
                            </div>
                            <div v-else class="truncate max-w-[120px]" :title="displayValue((subRow as any)[colMap(col)])">
                              {{ displayValue((subRow as any)[colMap(col)]) }}
                            </div>
                          </td>
                        </tr>
                      </template>
                    </template>
                  </template>
                </template>
              </template>
            </template>
          </template>
        </tbody>
      </table>
    </div>
  </div>
</template>

<style scoped>
table {
  border-collapse: separate;
  border-spacing: 0;
  font-size: small;
}

/* Proper spacing and alignment for flex containers */
.flex.items-center {
  align-items: center;
}

/* Ensure SVG icons are properly aligned */
.flex.items-center svg {
  flex-shrink: 0;
  vertical-align: middle;
}

/* Better text truncation */
.truncate {
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Consistent borders for all cells */
td {
  border-right: 1px solid #d1d5db;
  border-bottom: 1px solid #e5e7eb;
}

/* Z-index classes */
.z-50 {
  z-index: 50;
}

.z-49 {
  z-index: 49;
}

.z-48 {
  z-index: 48;
}

.z-47 {
  z-index: 47;
}

.z-46 {
  z-index: 46;
}

.z-40 {
  z-index: 40;
}

.z-39 {
  z-index: 39;
}

.z-38 {
  z-index: 38;
}

.z-37 {
  z-index: 37;
}

.z-36 {
  z-index: 36;
}
</style>