<template>
  <div class="min-h-screen bg-gradient-header py-8 px-4">
    <div class="max-w-7xl mx-auto animate-slideUp">
      <!-- Header -->
      <div class="mb-10">
        <h1 class="text-4xl font-bold text-gray-900 mb-3">
          Concept Selection
        </h1>
      </div>

      <!-- Concepts Section -->
      <div class="mb-12">
        <h2 class="text-2xl font-semibold text-gray-900 mb-6 flex items-center">
          <div class="w-1 h-6 rounded-full mr-3"></div>
          Concepts
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5">
          <label
            v-for="concept in concepts"
            :key="concept.id"
            class="bg-white rounded-2xl p-6 shadow-lg border-2 cursor-pointer transition-all duration-300 hover:shadow-xl hover:-translate-y-2 relative overflow-hidden group flex flex-row items-center gap-3"
            :class="selectedConcept?.id === concept.id ? 'border-blue-300' : 'border-transparent hover:border-blue-200'"
          >
            <input
              type="radio"
              name="concept"
              :value="concept"
              v-model="selectedConcept"
              class="accent-blue-500 w-5 h-5"
            />
            <img :src="concept.img" alt="concept logo" class="min-h-[50px]  object-contain" />
          </label>
        </div>
      </div>

      <!-- Territories and Season Section -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-10 mb-10">
        <!-- Territories -->
        <div class="bg-white rounded-2xl p-8 shadow-lg">
          <h3 class="text-xl font-semibold text-gray-900 mb-5">Territories</h3>
          <div class="grid grid-cols-2 gap-4">
            <label
              v-for="territory in availableTerritories"
              :key="territory.code"
              class="p-3 border-2 rounded-xl font-medium transition-all duration-300 flex items-center gap-2 cursor-pointer hover:-translate-y-1"
              :class="selectedTerritories[0]?.code === territory.code ? 'border-blue-300 bg-blue-50 text-blue-700 shadow-lg' : 'border-gray-200 hover:border-blue-200'"
            >
              <input
                type="radio"
                name="territory"
                :value="territory"
                v-model="selectedTerritories[0]"
                class="accent-blue-500 w-5 h-5"
              />
              <span class="text-left">{{ territory.code }}</span>
            </label>
          </div>
        </div>

        <!-- Season Selection -->
        <div class="bg-white rounded-2xl p-8 shadow-lg">
          <h3 class="text-xl font-semibold text-gray-900 mb-5">Season Selection</h3>
          <div class="bg-gray-100 rounded-2xl p-2 flex">
            <label
              v-for="season in seasons"
              :key="season.id"
              class="flex-1 py-3 px-6 rounded-xl font-medium transition-all duration-300 relative z-10 flex items-center justify-center cursor-pointer"
              :class="selectedSeason?.id === season.id ? 'bg-blue-200 text-blue-900 shadow-lg' : 'text-gray-600 hover:text-gray-900'"
            >
              <input
                type="radio"
                name="season"
                :value="season"
                v-model="selectedSeason"
                class="accent-blue-500 mr-2 w-5 h-5"
              />
              <span>{{ season.name }}</span>
            </label>
          </div>
        </div>
      </div>

      <!-- Store Locations Section -->
      <div 
        class="bg-white rounded-2xl p-8 shadow-lg animate-fadeIn"
      >
        <h2 class="text-2xl font-semibold text-gray-900 mb-6 flex items-center">
          <div class="w-1 h-6 bg-blue-300 rounded-full mr-3"></div>
          Stores
        </h2>
        <!-- Store Dropdown Button -->
        <div class="relative" ref="storeDropdownRef">
          <button
            type="button"
            class="w-full py-3 px-4 border-2 border-gray-200 rounded-xl font-medium text-gray-700 bg-gray-50 hover:bg-white hover:border-blue-300 transition-all duration-200 text-left flex items-center justify-between"
            @click="storeDropdownOpen = !storeDropdownOpen"
          >
            <span>
              <span v-if="selectedStore.length === 0">Select stores</span>
              <span v-else>{{ selectedStore.length }} selected</span>
            </span>
            <!-- Down Arrow SVG -->
            <svg class="w-5 h-5 text-gray-400 ml-2 flex-shrink-0" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24" stroke-linecap="round" stroke-linejoin="round"><path d="M19 9l-7 7-7-7" /></svg>
          </button>
          <div
            v-if="storeDropdownOpen"
            class="absolute left-0 mt-2 w-full bg-white border-2 border-blue-200 rounded-xl shadow-xl z-50"
          >
            <!-- Search Bar with Icon -->
            <div class="p-3 border-b border-gray-100">
              <div class="relative w-full" @mousedown.prevent="storeSearchInputRef && storeSearchInputRef.focus()">
                <span class="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none">
                  <!-- Search Icon SVG -->
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8" /><line x1="21" y1="21" x2="16.65" y2="16.65" /></svg>
                </span>
                <input
                  v-model="storeSearchTerm"
                  type="text"
                  class="w-full pl-10 pr-3 py-2 border border-gray-200 rounded-lg text-base focus:outline-none focus:border-blue-300"
                  placeholder="Search stores..."
                  ref="storeSearchInputRef"
                />
              </div>
            </div>
            <!-- Store List -->
            <div class="max-h-60 overflow-y-auto p-2 custom-scrollbar">
              <div
                v-for="location in filteredDropdownStores"
                :key="location.code"
                class="flex items-center p-3 rounded-lg cursor-pointer transition-all duration-200 mb-1 hover:bg-blue-50"
              >
                <input
                  type="checkbox"
                  :value="location"
                  v-model="selectedStore"
                  class="accent-blue-500 w-5 h-5 mr-3"
                />
                <span class="font-semibold text-blue-700 mr-3">{{ location.code }}</span>
                <span class="text-gray-700">{{ location.name }}</span>
              </div>
              <div v-if="filteredDropdownStores.length === 0" class="text-center py-8 text-gray-500">
                No stores found
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- Submit Button (Always Visible) -->
      <div class="max-w-7xl mx-auto mt-8">
        <button
          class="w-full py-3 rounded-xl font-semibold text-white transition-all duration-300 filter-btn"
          :class="{ 'opacity-50 cursor-not-allowed': !canSubmit }"
          :disabled="!canSubmit"
          @click="handleSubmit"
        >
          Submit
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onBeforeUnmount, inject } from 'vue'
import { useRouter } from 'vue-router'

const baseImgUrl = inject('baseImageUrl');

// Types
interface Concept {
  id: number;
  name: string;
  img: string;
  territories: string[];
}
interface Territory {
  code: string;
  name: string;
}
interface Season {
  id: number;
  name: string;
}
interface Store {
  code: string;
  name: string;
  concept: string;
  territories: string[];
}

// Initialize router
const router = useRouter()

// Reactive data
const selectedConcept = ref<Concept | null>(null)
const selectedTerritories = ref<Territory[]>([])
const selectedSeason = ref<Season | null>(null)
const searchTerm = ref('')
const selectedStore = ref<Store[]>([])
const jsonResponse = ref("")

// Store Dropdown State
const storeDropdownOpen = ref(false)
const storeSearchTerm = ref('')
const storeDropdownRef = ref<HTMLElement | null>(null)
const storeSearchInputRef = ref<HTMLInputElement | null>(null)

// Static data
const concepts: Concept[] = [
  { id: 1, name: 'Lifestyle', img: `${baseImgUrl}/lifestyle.png`, territories: ['UAE', 'KSA', 'QT', 'OM', 'KU', 'BH'] },
  { id: 2, name: 'Splash', img: `${baseImgUrl}/splash.png`, territories: ['UAE', 'KSA', 'QT', 'OM'] },
  { id: 3, name: 'HOME BOX', img: `${baseImgUrl}/homebox.png`, territories: ['UAE', 'KSA', 'QT', 'OM', 'KU', 'BH'] },
  { id: 4, name: 'babyshop', img: `${baseImgUrl}/babyshop.png`, territories: ['UAE', 'KSA', 'QT'] }
]

const allTerritories: Territory[] = [
  { code: 'UAE', name: 'United Arab Emirates' },
  { code: 'KSA', name: 'Saudi Arabia' },
  { code: 'QT', name: 'Qatar' },
  { code: 'OM', name: 'Oman' },
  { code: 'KU', name: 'Kuwait' },
  { code: 'BH', name: 'Bahrain' },
  { code: 'EG', name: 'Egypt' },
]

const seasons: Season[] = [
  { id: 1, name: 'In-Season' },
  { id: 2, name: 'Pre-Season' }
]

// Sample store data - this would come from an API
const allStores: Store[] = [
  // HOME BOX
  { code: '29052', name: 'Home Box Deerfields', concept: 'HOME BOX', territories: ['UAE'] },
  { code: '29064', name: 'Home Box Reem Mall', concept: 'HOME BOX', territories: ['UAE'] },
  { code: '29030', name: 'Home Box Sharjah City Centre', concept: 'HOME BOX', territories: ['UAE'] },
  { code: '29065', name: 'Home Box Mall of Emirates', concept: 'HOME BOX', territories: ['UAE'] },
  { code: '29066', name: 'Home Box Ibn Battuta', concept: 'HOME BOX', territories: ['UAE'] },
  { code: '29067', name: 'Home Box Riyadh Gallery', concept: 'HOME BOX', territories: ['KSA'] },
  { code: '29068', name: 'Home Box Granada Mall', concept: 'HOME BOX', territories: ['KSA'] },
  { code: '29069', name: 'Home Box Red Sea Mall', concept: 'HOME BOX', territories: ['KSA'] },
  { code: '29070', name: 'Home Box Al Nakheel', concept: 'HOME BOX', territories: ['KSA'] },
  { code: '29071', name: 'Home Box Panorama Mall', concept: 'HOME BOX', territories: ['KSA'] },
  { code: '29072', name: 'Home Box Doha Festival', concept: 'HOME BOX', territories: ['QT'] },
  { code: '29073', name: 'Home Box City Center', concept: 'HOME BOX', territories: ['QT'] },
  { code: '29074', name: 'Home Box Mall of Qatar', concept: 'HOME BOX', territories: ['QT'] },
  { code: '29075', name: 'Home Box Landmark', concept: 'HOME BOX', territories: ['QT'] },
  { code: '29076', name: 'Home Box Ezdan Mall', concept: 'HOME BOX', territories: ['QT'] },
  { code: '29077', name: 'Home Box Muscat Grand Mall', concept: 'HOME BOX', territories: ['OM'] },
  { code: '29078', name: 'Home Box Oman Avenues', concept: 'HOME BOX', territories: ['OM'] },
  { code: '29079', name: 'Home Box City Centre Muscat', concept: 'HOME BOX', territories: ['OM'] },
  { code: '29080', name: 'Home Box Panorama Mall Oman', concept: 'HOME BOX', territories: ['OM'] },
  { code: '29081', name: 'Home Box Seeb Mall', concept: 'HOME BOX', territories: ['OM'] },
  { code: '29082', name: 'Home Box The Avenues', concept: 'HOME BOX', territories: ['KU'] },
  { code: '29083', name: 'Home Box Marina Mall', concept: 'HOME BOX', territories: ['KU'] },
  { code: '29084', name: 'Home Box 360 Mall', concept: 'HOME BOX', territories: ['KU'] },
  { code: '29085', name: 'Home Box Al Kout Mall', concept: 'HOME BOX', territories: ['KU'] },
  { code: '29086', name: 'Home Box Gate Mall', concept: 'HOME BOX', territories: ['KU'] },
  { code: '29087', name: 'Home Box City Centre Bahrain', concept: 'HOME BOX', territories: ['BH'] },
  { code: '29088', name: 'Home Box Seef Mall', concept: 'HOME BOX', territories: ['BH'] },
  { code: '29089', name: 'Home Box The Avenues Bahrain', concept: 'HOME BOX', territories: ['BH'] },
  { code: '29090', name: 'Home Box Oasis Mall', concept: 'HOME BOX', territories: ['BH'] },
  { code: '29091', name: 'Home Box Dana Mall', concept: 'HOME BOX', territories: ['BH'] },

  // Lifestyle
  { code: '30001', name: 'Lifestyle Dubai Mall', concept: 'Lifestyle', territories: ['UAE'] },
  { code: '30002', name: 'Lifestyle Mall of Emirates', concept: 'Lifestyle', territories: ['UAE'] },
  { code: '30003', name: 'Lifestyle Ibn Battuta', concept: 'Lifestyle', territories: ['UAE'] },
  { code: '30004', name: 'Lifestyle Deira City Centre', concept: 'Lifestyle', territories: ['UAE'] },
  { code: '30005', name: 'Lifestyle Mirdif City Centre', concept: 'Lifestyle', territories: ['UAE'] },
  { code: '30006', name: 'Lifestyle Riyadh Gallery', concept: 'Lifestyle', territories: ['KSA'] },
  { code: '30007', name: 'Lifestyle Granada Mall', concept: 'Lifestyle', territories: ['KSA'] },
  { code: '30008', name: 'Lifestyle Red Sea Mall', concept: 'Lifestyle', territories: ['KSA'] },
  { code: '30009', name: 'Lifestyle Al Nakheel', concept: 'Lifestyle', territories: ['KSA'] },
  { code: '30010', name: 'Lifestyle Panorama Mall', concept: 'Lifestyle', territories: ['KSA'] },
  { code: '30011', name: 'Lifestyle Doha Festival', concept: 'Lifestyle', territories: ['QT'] },
  { code: '30012', name: 'Lifestyle City Center', concept: 'Lifestyle', territories: ['QT'] },
  { code: '30013', name: 'Lifestyle Mall of Qatar', concept: 'Lifestyle', territories: ['QT'] },
  { code: '30014', name: 'Lifestyle Landmark', concept: 'Lifestyle', territories: ['QT'] },
  { code: '30015', name: 'Lifestyle Ezdan Mall', concept: 'Lifestyle', territories: ['QT'] },
  { code: '30016', name: 'Lifestyle Muscat Grand Mall', concept: 'Lifestyle', territories: ['OM'] },
  { code: '30017', name: 'Lifestyle Oman Avenues', concept: 'Lifestyle', territories: ['OM'] },
  { code: '30018', name: 'Lifestyle City Centre Muscat', concept: 'Lifestyle', territories: ['OM'] },
  { code: '30019', name: 'Lifestyle Panorama Mall Oman', concept: 'Lifestyle', territories: ['OM'] },
  { code: '30020', name: 'Lifestyle Seeb Mall', concept: 'Lifestyle', territories: ['OM'] },
  { code: '30021', name: 'Lifestyle The Avenues', concept: 'Lifestyle', territories: ['KU'] },
  { code: '30022', name: 'Lifestyle Marina Mall', concept: 'Lifestyle', territories: ['KU'] },
  { code: '30023', name: 'Lifestyle 360 Mall', concept: 'Lifestyle', territories: ['KU'] },
  { code: '30024', name: 'Lifestyle Al Kout Mall', concept: 'Lifestyle', territories: ['KU'] },
  { code: '30025', name: 'Lifestyle Gate Mall', concept: 'Lifestyle', territories: ['KU'] },
  { code: '30026', name: 'Lifestyle City Centre Bahrain', concept: 'Lifestyle', territories: ['BH'] },
  { code: '30027', name: 'Lifestyle Seef Mall', concept: 'Lifestyle', territories: ['BH'] },
  { code: '30028', name: 'Lifestyle The Avenues Bahrain', concept: 'Lifestyle', territories: ['BH'] },
  { code: '30029', name: 'Lifestyle Oasis Mall', concept: 'Lifestyle', territories: ['BH'] },
  { code: '30030', name: 'Lifestyle Dana Mall', concept: 'Lifestyle', territories: ['BH'] },

  // Splash
  { code: '31001', name: 'Splash Marina Mall', concept: 'Splash', territories: ['UAE'] },
  { code: '31002', name: 'Splash Dubai Mall', concept: 'Splash', territories: ['UAE'] },
  { code: '31003', name: 'Splash Ibn Battuta', concept: 'Splash', territories: ['UAE'] },
  { code: '31004', name: 'Splash Deira City Centre', concept: 'Splash', territories: ['UAE'] },
  { code: '31005', name: 'Splash Mirdif City Centre', concept: 'Splash', territories: ['UAE'] },
  { code: '31006', name: 'Splash Riyadh Gallery', concept: 'Splash', territories: ['KSA'] },
  { code: '31007', name: 'Splash Granada Mall', concept: 'Splash', territories: ['KSA'] },
  { code: '31008', name: 'Splash Red Sea Mall', concept: 'Splash', territories: ['KSA'] },
  { code: '31009', name: 'Splash Al Nakheel', concept: 'Splash', territories: ['KSA'] },
  { code: '31010', name: 'Splash Panorama Mall', concept: 'Splash', territories: ['KSA'] },
  { code: '31011', name: 'Splash Doha Festival', concept: 'Splash', territories: ['QT'] },
  { code: '31012', name: 'Splash City Center', concept: 'Splash', territories: ['QT'] },
  { code: '31013', name: 'Splash Mall of Qatar', concept: 'Splash', territories: ['QT'] },
  { code: '31014', name: 'Splash Landmark', concept: 'Splash', territories: ['QT'] },
  { code: '31015', name: 'Splash Ezdan Mall', concept: 'Splash', territories: ['QT'] },
  { code: '31016', name: 'Splash Muscat Grand Mall', concept: 'Splash', territories: ['OM'] },
  { code: '31017', name: 'Splash Oman Avenues', concept: 'Splash', territories: ['OM'] },
  { code: '31018', name: 'Splash City Centre Muscat', concept: 'Splash', territories: ['OM'] },
  { code: '31019', name: 'Splash Panorama Mall Oman', concept: 'Splash', territories: ['OM'] },
  { code: '31020', name: 'Splash Seeb Mall', concept: 'Splash', territories: ['OM'] },

  // babyshop
  { code: '32001', name: 'Babyshop City Centre', concept: 'babyshop', territories: ['UAE'] },
  { code: '32002', name: 'Babyshop Al Ghurair', concept: 'babyshop', territories: ['UAE'] },
  { code: '32003', name: 'Babyshop Dubai Mall', concept: 'babyshop', territories: ['UAE'] },
  { code: '32004', name: 'Babyshop Ibn Battuta', concept: 'babyshop', territories: ['UAE'] },
  { code: '32005', name: 'Babyshop Deira City Centre', concept: 'babyshop', territories: ['UAE'] },
  { code: '32006', name: 'Babyshop Riyadh Gallery', concept: 'babyshop', territories: ['KSA'] },
  { code: '32007', name: 'Babyshop Granada Mall', concept: 'babyshop', territories: ['KSA'] },
  { code: '32008', name: 'Babyshop Red Sea Mall', concept: 'babyshop', territories: ['KSA'] },
  { code: '32009', name: 'Babyshop Al Nakheel', concept: 'babyshop', territories: ['KSA'] },
  { code: '32010', name: 'Babyshop Panorama Mall', concept: 'babyshop', territories: ['KSA'] },
  { code: '32011', name: 'Babyshop Doha Festival', concept: 'babyshop', territories: ['QT'] },
  { code: '32012', name: 'Babyshop City Center', concept: 'babyshop', territories: ['QT'] },
  { code: '32013', name: 'Babyshop Mall of Qatar', concept: 'babyshop', territories: ['QT'] },
  { code: '32014', name: 'Babyshop Landmark', concept: 'babyshop', territories: ['QT'] },
  { code: '32015', name: 'Babyshop Ezdan Mall', concept: 'babyshop', territories: ['QT'] },
]

// Computed properties
const availableTerritories = computed(() => {
  if (!selectedConcept.value) return []
  return allTerritories.filter(territory => 
    selectedConcept.value && selectedConcept.value.territories.includes(territory.code)
  )
})

const filteredLocations = computed(() => {
  if (!selectedConcept.value || !selectedTerritories.value[0]) return []
  const conceptStores = allStores.filter(store => 
    selectedConcept.value &&
    store.concept === selectedConcept.value.name &&
    store.territories.includes(selectedTerritories.value[0].code)
  )
  if (!searchTerm.value) return conceptStores
  return conceptStores.filter(store =>
    store.name.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
    store.code.toLowerCase().includes(searchTerm.value.toLowerCase())
  )
})

// Filtered store list for dropdown
const filteredDropdownStores = computed(() => {
  if (!selectedConcept.value || !selectedTerritories.value[0]) return []
  const conceptStores = allStores.filter(store =>
    selectedConcept.value &&
    store.concept === selectedConcept.value.name &&
    store.territories.includes(selectedTerritories.value[0].code)
  )
  if (!storeSearchTerm.value) return conceptStores
  return conceptStores.filter(store =>
    store.name.toLowerCase().includes(storeSearchTerm.value.toLowerCase()) ||
    store.code.toLowerCase().includes(storeSearchTerm.value.toLowerCase())
  )
})

// Enable submit only if concept, territory, and at least one store are selected
const canSubmit = computed(() => {
  return !!(selectedConcept.value && selectedTerritories.value[0] && selectedStore.value.length > 0)
})

// Methods
const showJsonResponse = () => {
  if (canSubmit.value) {
    jsonResponse.value = JSON.stringify({
      concept: selectedConcept.value,
      territory: selectedTerritories.value[0],
      stores: selectedStore.value
    }, null, 2)
  }
}

const selectConcept = (concept: Concept) => {
  selectedConcept.value = concept
  selectedTerritories.value = []
  selectedSeason.value = seasons[0]
  searchTerm.value = ''
}

const toggleTerritory = (territory: Territory) => {
  selectedTerritories.value = [territory]
}

// Create a comprehensive function to handle submission
const handleSubmit = () => {
  if (canSubmit.value) {
    // Create complete response object with all selected data
    const responseData = {
      concept: selectedConcept.value,
      territory: selectedTerritories.value[0],
      season: selectedSeason.value,
      stores: selectedStore.value
    };
    
    // Log the complete response to console
    console.log('Selected options:', responseData);
    
    // Store in jsonResponse ref for potential use elsewhere
    jsonResponse.value = JSON.stringify(responseData, null, 2);
    
    // Navigate to store-clustering route
    router.push({ name: 'store-clustering' });
  }
}

// Click outside handler
const handleClickOutside = (event: MouseEvent) => {
  if (storeDropdownRef.value && !storeDropdownRef.value.contains(event.target as Node)) {
    storeDropdownOpen.value = false
  }
}

// Watchers
watch(selectedConcept, () => {
  selectedTerritories.value = []
  selectedSeason.value = seasons[0] // Default to In-Season
})

// On mount, set default season
onMounted(() => {
  document.addEventListener('mousedown', handleClickOutside)
  // Set default concept to Lifestyle
  selectedConcept.value = concepts[0]
  // Set default territories to Lifestyle's first territory
  const lifestyleTerritoryCode = concepts[0].territories[0]
  const defaultTerritory = allTerritories.find(t => t.code === lifestyleTerritoryCode)
  selectedTerritories.value = defaultTerritory ? [defaultTerritory] : []
  selectedSeason.value = seasons[0]
})

// On before unmount, remove event listener
onBeforeUnmount(() => {
  document.removeEventListener('mousedown', handleClickOutside)
})

// Remove the return statement - it's not needed in <script setup>
</script>

<style scoped>
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-slideUp {
  animation: slideUp 0.8s ease-out;
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out;
}

.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>
