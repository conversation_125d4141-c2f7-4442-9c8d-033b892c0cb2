<template>
    <div class="">       
        <div class="bg-primary">
            <h1 class="text-2xl font-bold text-green-800 ml-16">OPTIMIZATION HUB</h1>
            <h3 class="text-sm text-green-800 ml-16 pb-16">Track, Run & Review Optimizers</h3>
        </div>
        <div class="bg-secondary h-[5px] w-full"></div>
        <div class="p-8">
            <div class="max-w-7xl mx-auto border bg-white mt-[-8%] rounded-lg p-4">
                <!-- Header -->
                <div class="flex items-center mb-3 ml-2">
                    <p class="text-gray-800 font-medium mr-4">Get started with Optimization</p>
                    <button
                        class="flex items-center gap-1 text-sm bg-tertiary text-white font-semibold border border-primary px-3 py-1.5 rounded hover:bg-tertiary hover:text-white transition" @click="createOptimizer">
                        + Start Optimization
                    </button>
                </div>

                <!-- Search and Filter Bar -->
                <div class="flex items-center bg-white p-1 rounded shadow mb-4 w-1/2">
                    <input type="text" v-model="search" placeholder="Search optimizers..."
                        class="flex-1 px-4 h-10 py-2 border rounded" />
                </div>

                <!-- Optimizer Sections -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Completed Optimizers -->
                    <div class="border-1 p-3 rounded shadow-md">
                        <h2 class="text-lg font-semibold mb-2 flex">
                            <CheckCircle class="flex bg text-secondary mr-3" /> Completed Optimizers <span
                                class="bg-primary ml-3 text-green-800 text-sm font-medium px-2 py-0.5 rounded">{{
                                completedOptimizers.length }}</span>
                        </h2>

                        <div v-for="opt in completedOptimizers" :key="opt.id"
                            class="bg-white rounded p-3 shadow mb-2 transition-transform transform hover:shadow-lg hover:scale-[1.01] hover:bg-gray-50 cursor-pointer">

                            <div class="flex items-center justify-between mb-3">
                                <h3 class="font-semibold"> {{ opt.name }}</h3>
                                <!-- <span class="text-xs bg-green-100 text-green-700 px-2 py-0.5 rounded">Completed</span> -->
                            </div>
                            <div class="flex">
                            <div class="text-xs text-gray-500 mb-2">Event: {{ opt.event }} | Evaluation Period: {{ opt.created }} - {{ opt.completed }}</div>
                            </div>
                            <div class="flex gap-2">
                                <button
                                    class="text-xs px-2 py-1 rounded bg-green-100 text-green-800 hover:bg-green-200 transition">Evaluation
                                    Summary</button>
                            </div>
                        </div>
                    </div>

                    <!-- Stopped Optimizers -->
                    <div class="border-1 p-3 rounded shadow-md">
                        <h2 class="text-lg font-semibold mb-2 flex">
                            <PauseCircle class="text-orange-500 mr-3" /> Ongoing Optimizers <span
                                class="ml-3 bg-blue-100 text-blue-700 text-sm font-medium px-2 py-0.5 rounded">{{
                                stoppedOptimizers.length }}</span>
                        </h2>

                        <div v-for="opt in stoppedOptimizers" :key="opt.id"
                            class="bg-white rounded p-3 shadow mb-2 transition-transform transform hover:shadow-lg hover:scale-[1.01] hover:bg-gray-50 cursor-pointer">

                            <div class="flex items-center justify-between">
                                <h3 class="flex font-semibold mb-2"> {{ opt.name }}</h3>

                                <div class="flex justify-end items-center space-x-2">
                                    <!-- <span
                                        class="text-xs bg-blue-100 text-blue-700 px-2 py-0.5 rounded">Ongoing</span> -->
                                    <button @click="resumeOptimizer(opt.id)"
                                        class="p-1 flex items-center text-orange-500 hover:text-orange-800 hover:bg-orange-100 rounded-lg transition-colors duration-200"
                                        title="Resume optimizer">
                                        <Play class="w-4 h-4 flex mr-1" />Resume
                                    </button>
                                </div>
                            </div>
                            <div class="text-xs text-gray-500 mb-2">Event: {{ opt.event }} | Evaluation Period: {{ opt.created }} - {{ opt.stopped }}</div>
                            <div class="flex items-center">
                                <div class="flex h-1 bg-gray-200 rounded w-1/4 mr-3">
                                    <div class="h-1 bg-orange-300 rounded" :style="{ width: opt.progress + '%' }"></div>
                                </div>
                                <div class="flex text-xs text-gray-600">{{ opt.description }}</div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import Sidebar from './Sidebar.vue'
import { Activity, CheckCircle, PauseCircle, Play } from 'lucide-vue-next';
import { useRouter } from 'vue-router'

const router = useRouter()
const search = ref('');
const isExpanded = ref(false)
const expandSidebar = () => {
  isExpanded.value = true;
};

const collapseSidebar = () => {
  isExpanded.value = false;
};

const createOptimizer = () => {
    router.push({ name: 'BuildOptimiser' })
};

const optimizers = ref([
    {
        id: 1,
        name: 'home box UAT',
        description: 'Step 2 of step 7 completed.',
        event: 'Eid',
        status: 'completed',
        created: '1/15/2024',
        completed: '1/16/2024',
        accuracy: '94.2%',
        loss: '0.125',
        iterations: 500,
        progress: 100
    },
    {
        id: 2,
        name: 'LS bts 2024',
        description: 'Step 2 of step 7 completed.',
        event: 'Eid',
        status: 'Ongoing',
        created: '1/14/2024',
        stopped: '1/14/2024',
        accuracy: '87.1%',
        loss: '0.245',
        iterations: 325,
        progress: 65
    },
    {
        id: 3,
        name: 'splash optimizer',
        description: 'Step 2 of step 7 completed.',
        event: 'Eid',
        status: 'completed',
        created: '1/13/2024',
        completed: '1/14/2024',
        accuracy: '92.3%',
        loss: '0.095',
        iterations: 460,
        progress: 100
    },
    {
        id: 4,
        name: 'Marketing optimisers',
        description: 'Step 2 of step 7 completed.',
        event: 'Eid',
        status: 'Ongoing',
        created: '1/13/2024',
        stopped: '1/14/2024',
        accuracy: '88.6%',
        loss: '0.214',
        iterations: 250,
        progress: 70
    },
    {
        id: 4,
        name: 'Supply Chain Route Optimization',
        description: 'Step 2 of step 7 completed.',
        event: 'Eid',
        status: 'completed',
        created: '1/13/2024',
        completed: '1/14/2024',
        accuracy: '92.3%',
        loss: '0.095',
        iterations: 460,
        progress: 100
    },
    {
        id: 5,
        name: 'LS UAT',
        description: 'Step 2 of step 7 completed.',
        event: 'Eid',
        status: 'Ongoing',
        created: '1/13/2024',
        stopped: '1/14/2024',
        accuracy: '88.6%',
        loss: '0.214',
        iterations: 250,
        progress: 70
    },
    {
        id: 6,
        name: 'Supply Chain Route Optimization',
        description: 'Step 2 of step 7 completed.',
        event: 'Eid',
        status: 'completed',
        created: '1/13/2024',
        completed: '1/14/2024',
        accuracy: '92.3%',
        loss: '0.095',
        iterations: 460,
        progress: 100
    },
    {
        id: 7,
        name: 'LS scenarios',
        description: 'Step 2 of step 7 completed.',
        event: 'Eid',
        status: 'Ongoing',
        created: '1/13/2024',
        stopped: '1/14/2024',
        accuracy: '88.6%',
        loss: '0.214',
        iterations: 250,
        progress: 70
    }
]);

const completedOptimizers = computed(() =>
    optimizers.value.filter(
        o =>
            o.status === 'completed' &&
            o.name.toLowerCase().includes(search.value.toLowerCase())
    )
);

const stoppedOptimizers = computed(() =>
    optimizers.value.filter(
        o =>
            o.status === 'Ongoing' &&
            o.name.toLowerCase().includes(search.value.toLowerCase())
    )
);

const resumeOptimizer = (id) => {
    console.log(`Resume optimizer with ID: ${id}`);
};
</script>

<style scoped>
input:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.5);
}
</style>